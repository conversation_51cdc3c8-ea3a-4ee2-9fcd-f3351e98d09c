-- 键盘监听功能测试脚本
-- 这个脚本用于测试键盘监听和路径切换功能

-- 模拟罗技G-series API函数 (用于测试)
function OutputLogMessage(format, ...)
    print(string.format(format, ...))
end

function IsKeyPressed(key)
    -- 这里应该是实际的键盘检测逻辑
    -- 在实际的罗技环境中，这个函数会检测真实的键盘状态
    return false  -- 测试时返回false
end

-- 包含主脚本的键盘监听变量和函数
local weapon_path = "C:/Temp/weapon.lua"
local jia_path = "C:/Temp/jia.lua"
local current_path = weapon_path
local addr = current_path
local keyboard_monitoring_enabled = true

-- 路径切换函数
function switch_to_jia_path()
    if current_path ~= jia_path then
        current_path = jia_path
        addr = current_path
        OutputLogMessage("键盘监听: 路径已切换到 %s\n", current_path)
        return true
    end
    return false
end

function switch_to_weapon_path()
    if current_path ~= weapon_path then
        current_path = weapon_path
        addr = current_path
        OutputLogMessage("键盘监听: 路径已切换到 %s\n", current_path)
        return true
    end
    return false
end

-- 键盘监听处理函数
function handle_keyboard_input(key)
    if not keyboard_monitoring_enabled then
        return false
    end
    
    -- 检查是否是我们监听的键位
    if key == "g" or key == "x" or key == "3" or key == "4" then
        -- 切换到 jia.lua
        return switch_to_jia_path()
    elseif key == "1" or key == "2" then
        -- 切换到 weapon.lua
        return switch_to_weapon_path()
    end
    
    return false
end

-- 测试函数
function test_keyboard_monitoring()
    print("=== 键盘监听功能测试 ===")
    print("当前路径:", current_path)
    
    -- 测试切换到 jia.lua
    print("\n测试按键 'g' (应该切换到 jia.lua):")
    local changed = handle_keyboard_input("g")
    print("路径是否改变:", changed)
    print("当前路径:", current_path)
    
    -- 测试切换到 weapon.lua
    print("\n测试按键 '1' (应该切换到 weapon.lua):")
    changed = handle_keyboard_input("1")
    print("路径是否改变:", changed)
    print("当前路径:", current_path)
    
    -- 测试切换到 jia.lua
    print("\n测试按键 'x' (应该切换到 jia.lua):")
    changed = handle_keyboard_input("x")
    print("路径是否改变:", changed)
    print("当前路径:", current_path)
    
    -- 测试无效按键
    print("\n测试按键 'z' (应该无效果):")
    changed = handle_keyboard_input("z")
    print("路径是否改变:", changed)
    print("当前路径:", current_path)
    
    print("\n=== 测试完成 ===")
end

-- 运行测试
test_keyboard_monitoring()
