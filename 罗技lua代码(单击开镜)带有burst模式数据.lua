EnablePrimaryMouseButtonEvents(true)
pick = 5 --（鬼手按键默认鼠标上侧键，3是中键，4是下侧键，5是上侧键,如果不想用这几个按键，就改成超过5的数字）

global_vertical_sensitivity_multiplier = 121/100  --垂直灵敏度比如100的意思就是垂直灵敏度1

first_shot_offset = 0.2 -- 第一发子弹向下偏移量，例如 0.2


global_breath_multiplier = 1  --屏息灵敏度(要自己调）

global_scope_multipliers = { --倍镜灵敏度，分子+-1范围内调整
    None = 1,
    reddot = 1,
    quanxi = 1,
    x2 = 99.1/64.52,
    x3 = 104/72.41,
    x4 = 103/67.21,
    x6 = 109.5/75.86,
}    

x_accumulator = 5 
 
 function handle_x_movement(now_cursor_x)
     move_x = 0
    if now_cursor_x > 0 then
        x_accumulator = x_accumulator - 0.5 
    elseif now_cursor_x < 0 then
        x_accumulator = x_accumulator + 0.5  
    end
    
    if math.abs(x_accumulator) >= 1 then
        move_x = math.floor(x_accumulator)
        x_accumulator = x_accumulator - move_x
    end
    return move_x
end

global_sensitivity_multiplier = 30/75.2


 base_coefficients = { --各枪械总系数，比如只有某把枪所有配件都下压过度，则调这个
    Berry = 1,
    AUG = 1.0,
    AKM = 1.0,
    M416 = 1.0,
    ACE32 = 1.0,
    G36C = 1.0,
    SCAR = 1.0,
    QBZ = 1.0,
    K2 = 1.0,
    M16 = 1.0,
    MK47 = 1.1,
    GROZA = 1.0,
    FAMAS = 1.0,
    PP19 = 1.0,
    TOM = 1.0,
    UMP = 1.0,
    UZI = 1.0,
    VECTOR = 1.0,
    MP5 = 1.0,
    P90 = 1.0,
    JS9 = 1.0,
    MP9 = 1.0,
    SLR = 1.5,
    MINI = 1.7,
    SKS = 1.05,
    MK12 = 1.7,
    QBU = 1.55,
    DLG = 1.0,
    VSS = 1.0,
    MK14 = 1.8,
    M249 = 1.0,
    MG3 = 1.0,
}          
            

                        

 attachment_multipliers = {
 None = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
            
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.78,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455, 
            
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },
    Berry = { 
        poses = {
            None = 1.02,
            stand = 1.02,
            down = 0.83,
            crawl = 0.58,
             
        },
        muzzles = {
            None = 1,
            xy1 = 0.85,
            bc1 = 0.76,
            xx = 1,
            zt = 0.87,
        },
         grips = {
            None = 1,
            angle = 1,
            red = {  
                segments = { 
                    { count = 8, multiplier = 0.9 },
                    { count = 40, multiplier = 0.8 },
                }
            },
            line = 0.78,
             thumb = {  
                segments = { 
                    { count = 17, multiplier = 0.8 },
                    { count = 40, multiplier = 0.75 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.9 },
                    
                    { count = 40, multiplier = 0.85 },
                }
            },
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.444,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   AUG = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.8,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.84,
            bc1 = 0.78,
            xx = 1,
            zt = 0.865,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = {  
                segments = {
                    { count = 10, multiplier = 0.9 },
                    { count = 30, multiplier = 0.78 },    
                    { count = 40, multiplier = 0.85 },
                    
                }
            },
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 3, multiplier = 0.8 },
                    { count = 10, multiplier = 0.82 },
                    { count = 40, multiplier = 0.78 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.9 },
                    
                    { count = 40, multiplier = 0.84 },
                }
            },
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.444,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   AKM = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.47,
        },
        muzzles = {
            None = 1,
            xy1 = 0.85,
            bc1 = 0.76,
            xx = 1,
            zt = 0.86,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.55,
            reddot = 0.55,
            quanxi = 0.55,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.444,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   M416 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.8,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.79,
            xx = 1,
            zt = 0.89,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = {  
                segments = {
                    { count = 8, multiplier = 0.9 },
                     { count = 27, multiplier = 0.8 },
                      { count = 40, multiplier = 0.9 },

                    
                    
                }
            },
            line = 0.8,
            thumb = {  
                segments = { 
                    { count = 3, multiplier = 0.8 },
                    { count = 10, multiplier = 0.82 },
                    { count = 30, multiplier = 0.77 },
                    { count = 40, multiplier = 0.8 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.9 },
                    
                    { count = 40, multiplier = 0.84 },
                }
            },
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
            normal = {  
                segments = {  
                    { count = 2, multiplier = 0.87 },
                    { count = 20, multiplier = 0.94 },
                    { count = 40, multiplier = 1.02 },
                }
            },
            heavy = {  
                segments = {  
                    { count = 11, multiplier = 0.9 },
                     { count = 25, multiplier = 0.78 },
                      { count = 37, multiplier = 0.88 },
                      { count = 40, multiplier = 0.92 },
                }
            },
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   ACE32 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
             red = {  
                segments = {
                    { count = 6, multiplier = 1 },
                    { count = 25, multiplier = 0.8 },
                    { count = 40, multiplier = 0.73 },
                   
                    

                   
                }
            },
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 3, multiplier = 0.8 },
                    { count = 10, multiplier = 0.82 },
                    { count = 30, multiplier = 0.74 },
                    { count = 40, multiplier = 0.72 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.92 },
                    
                    { count = 40, multiplier = 0.8 },
                }
            },
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
            normal = {  
                segments = {  
                    { count = 7, multiplier = 1.02 },
                    { count = 27, multiplier = 0.97 },
                     { count = 40, multiplier = 0.92 },
                }
            },
            heavy = {  
                segments = {  
                    { count = 11, multiplier = 0.9 },
                     { count = 25, multiplier = 0.78 },
                      { count = 37, multiplier = 0.88 },
                      { count = 40, multiplier = 0.92 },
                }
            },
        
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   G36C = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.79,
            line = 0.78,
            thumb = 0.76,
            light = 0.79,
        },
        scopes = {
            None = 0.55,
            reddot = 0.55,
            quanxi = 0.55,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   SCAR = { 
        poses = {
            None = 0.96,
            stand = 0.96,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.84,
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 17, multiplier = 0.82 },
                    { count = 40, multiplier = 0.85 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.9 },
                    
                    { count = 40, multiplier = 0.85 },
                }
            },
        },
        scopes = {
            None = 0.5,
            reddot = 0.5,
            quanxi = 0.5,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   QBZ = { 
        poses = {
            None = 0.92,
            stand = 0.92,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.88,
            bc1 = 0.8,
            xx = 1,
            zt = 0.88,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = {  
                segments = {
                    { count = 6, multiplier = 0.9 },
                    { count = 12, multiplier = 0.8 },
                    { count = 40, multiplier = 0.75 },
                    

                   
                }
            },
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 12, multiplier = 0.82 },
                    { count = 40, multiplier = 0.84 },
                }
            },
            light = 0.79,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   K2 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.55,
            reddot = 0.55,
            quanxi = 0.55,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   M16 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.83,
            bc1 = 0.74,
            xx = 1,
            zt = 0.84,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1.8,
            x3 = 2.6,
            x4 = 3.6,
            x6 = 2.6,

        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = {  
                segments = {  
                      { count = 15, multiplier = 0.92 },
                      { count = 40, multiplier = 0.81 },
                }
            },
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   MK47 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.53,
        },
        muzzles = {
            None = 1,
            xy1 = 0.84,
            bc1 = 0.75,
            xx = 1,
            zt = 0.84,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.86,
            line = 0.78,
            thumb = {  
                segments = { 
                    { count = 12, multiplier = 0.8 },
                    { count = 40, multiplier = 0.85 },
                }
            },
            light = {  
                segments = {  
                    { count = 12, multiplier = 0.92 },
                    
                    { count = 40, multiplier = 0.99 },
                }
            },
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1.8,
            x3 = 2.6,
            x4 = 3.6,
            x6 = 2.6,

        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   GROZA = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.7,
            crawl = 0.47,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   FAMAS = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.8,
            xx = 1,
            zt = 0.83,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   PP19 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.77,
            crawl = 0.7,
        },
        muzzles = {
            None = 1,
            bc3 = 0.8,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
            line = 0.78,
        },
        scopes = {
            None = 0.51,
            reddot = 0.51,
            quanxi = 0.51,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   TOM = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.7,
            crawl = 0.62,
        },
        muzzles = {
            None = 1,
            xx1 = 1,
        },
        grips = {
            None = 1,
            line = 0.78	,
        },
        scopes = {
            None = 0.51,
            reddot = 0.51,
            quanxi = 0.51,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   UMP = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.74,
            crawl = 0.7,
        },
        muzzles = {
            None = 1,
            bc3 = 0.8,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.935,
            line = 0.79,
            thumb = 0.88,
            light = 0.94,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   UZI = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.73,
            crawl = 0.6,
        },
        muzzles = {
            None = 1,
            bc3 = 0.68,
            xy3 = 0.88,
            xx1 = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.51,
            reddot = 0.51,
            quanxi = 0.51,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   VECTOR = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.64,
        },
        muzzles = {
            None = 1,
            bc3 = 0.8,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
            red = 0.9,
            line = 0.79,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
            normal = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   MP5 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.67,
            crawl = 0.53,
        },
        muzzles = {
            None = 1,
            bc3 = 0.6,
            xy3 = 0.82,
            xx1 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.75,
            thumb = 0.83,
            light = 0.9,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
            normal = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   P90 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.77,
            crawl = 0.67,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   JS9 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.73,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            bc3 = 0.58,
            xy3 = 0.82,
            xx1 = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   MP9 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.73,
            crawl = 0.63,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
 
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },
 SLR = { 
        poses = {
            None = 1,
            stand = 1.02,
            down = 0.77,
            crawl = 0.45,            
            standBurst = 1.25, 
            downBurst = 0.82    
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.88,
                   
        },
        scopes = {
            None = 0.46,
            reddot = 0.46,
            quanxi = 0.46,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 0.9,
            x8 = 1,
            x4burst = 1.2,
            x8burst = 0.7,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    MINI = { 
        poses = {
            None = 1.05,
            stand = 1.05,
            down = 0.72,
            crawl = 0.45,
            standBurst = 1.46, 
            downBurst = 1    
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.89,
                   
        },
        scopes = {
            None = 0.4,
            reddot =0.4,
            quanxi = 0.4,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 1,
            x8 = 1.1,
            x4burst = 1.4,
            x8burst = 0.8,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    SKS = { 
        poses = {
            None = 1,
            stand = 1.099,
            down = 2,
            crawl = 1.7,
            standBurst = 1.5, 
            downBurst = 1
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.87,
                   
        },
        scopes = {
            None = 0.465,
            reddot = 0.465,
            quanxi = 0.465,
            x2 = 1,
            x3 = 1,
            x4 = 0.6,
            x6 = 1,
            x8 = 0.6,
            x4burst = 1.37,
            x8burst = 0.8,

        },
         grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    MK12 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.45,
            standBurst = 1.43, 
            downBurst = 1 
        },
        
         muzzles = {
           None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 0.38,
            reddot = 0.38,
            quanxi = 0.38,
            x2 = 1,
            x3 = 1,
            x4 = 1.08,
            x6 = 1,
            x8 = 1.08,
            x4burst = 1.4,
            x8burst = 0.8,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    QBU = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.45, 
            standBurst = 1.43, 
            downBurst = 1  
        },
         muzzles = {
           None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 0.38,
            reddot = 0.38,
            quanxi = 0.38,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
            x4burst = 1.4,
            x8burst = 0.8,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    DLG = { 
        poses = {
            None = 1.3,
            stand = 1.3,
            down = 0.84, 
            crawl = 0.45,
            
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1.8,
            x4 = 1.5,
            x6 = 1.8,
            x8 = 1.5,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    VSS = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,  
        },
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 1,
                   
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
  
    MK14 = { 
        poses = {
            None = 1,
            stand = 0.95,
            down = 0.77,
            crawl = 0.45, 
            standBurst = 1, 
            downBurst = 1
        },
         muzzles = {
            None = 1,
            xy1 = 0.94,
            xy2 = 0.94,
            bc1 = 0.9,
            bc2 = 0.9,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 1,
            x8 = 1.2,

        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    M249 = { 
        poses = {
            None = 1.28,
            stand = 1.28,
            down = 0.73,
            crawl = 0.3,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.55,  
            reddot = 0.55, 
            quanxi = 0.55, 
	     x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
			 normal = {  
                segments = {  
                    { count = 90, multiplier = 1},
                    { count = 150, multiplier = 1.1 },
                }
            },
			heavy = {  
                segments = {  
                    { count = 90, multiplier = 0.83},
                     { count = 150, multiplier = 0.72 },

                }
            },
			
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

	MG3 = { 
        poses = {
            None = 3,
            stand = 3,
            down = 1.78,
            crawl = 0.65,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,  
            reddot = 0.52, 
            quanxi = 0.52, 
			x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,

        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },
}
    

 recoil_patterns = {
Berry = {
        default = {
{1, 18},
{2, 29},
{3, 17},
{4, 39},
{5, 21},
{6, 41},
{7, 21},
{8, 45},
{9, 25},
{10, 49},
{11, 25},
{12, 49},
{13, 25},
{14, 49},
{15, 29},
{16, 57},
{17, 30},
{18, 59},
{19, 30},
{20, 59},
{21, 30},
{22, 59},
{23, 30},
{24, 59},
{25, 30},
{26, 59},
{27, 30},
{28, 59},
{29, 30},
{30, 59},
{31, 30},
{32, 59},
{33, 30},
{34, 59},
{35, 30},
{36, 59},
{37, 30},
{38, 59},
{39, 30},
{40, 59},
{41, 30}
        },
    },
None = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 45},
{31, 23},
{32, 45},
{33, 23},
{34, 45},
{35, 23},
{36, 45},
{37, 23},
{38, 45},
{39, 23},
{40, 45},
{41, 23}
        },
    },
    

    AUG = {
        default = {
{1, 24},
{2, 17},
{3, 9},
{4, 25},
{5, 16},
{6, 35},
{7, 20},
{8, 39},
{9, 20},
{10, 41},
{11, 23},
{12, 49},
{13, 25},
{14, 49},
{15, 26},
{16, 51},
{17, 27},
{18, 53},
{19, 27},
{20, 53},
{21, 27},
{22, 53},
{23, 27},
{24, 53},
{25, 27},
{26, 53},
{27, 27},
{28, 53},
{29, 27},
{30, 53},
{31, 27},
{32, 55},
{33, 28},
{34, 55},
{35, 28},
{36, 55},
{37, 28},
{38, 55},
{39, 28},
{40, 55},
{41, 28}
        },
    },

    AKM = {
        default = {
{1, 16},
{2, 25},
{3, 13},
{4, 23},
{5, 16},
{6, 31},
{7, 16},
{8, 35},
{9, 20},
{10, 39},
{11, 21},
{12, 41},
{13, 21},
{14, 41},
{15, 21},
{16, 43},
{17, 22},
{18, 43},
{19, 22},
{20, 43},
{21, 22},
{22, 43},
{23, 22},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 43},
{31, 22},
{32, 43},
{33, 22},
{34, 43},
{35, 22},
{36, 43},
{37, 22},
{38, 43},
{39, 22},
{40, 43},
{41, 22}
        },
    },  

    M416 = {
        default = {
{1, 23},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 39},
{25, 19},
{26, 39},
{27, 19},
{28, 39},
{29, 19},
{30, 37},
{31, 18},
{32, 37},
{33, 18},
{34, 37},
{35, 18},
{36, 37},
{37, 18},
{38, 37},
{39, 18},
{40, 37},
{41, 18}
        },
    },

    ACE32 = {
        default = {
{1, 18},
{2, 23},
{3, 12},
{4, 29},
{5, 17},
{6, 37},
{7, 20},
{8, 39},
{9, 21},
{10, 41},
{11, 21},
{12, 43},
{13, 23},
{14, 45},
{15, 23},
{16, 45},
{17, 23},
{18, 47},
{19, 25},
{20, 51},
{21, 26},
{22, 51},
{23, 26},
{24, 51},
{25, 27},
{26, 53},
{27, 27},
{28, 53},
{29, 27},
{30, 53},
{31, 27},
{32, 53},
{33, 27},
{34, 53},
{35, 27},
{36, 53},
{37, 27},
{38, 53},
{39, 27},
{40, 53},
{41, 27}
        },
    },

    G36C = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 45},
{31, 23},
{32, 45},
{33, 23},
{34, 45},
{35, 23},
{36, 45},
{37, 23},
{38, 45},
{39, 23},
{40, 45},
        },
    },

    SCAR = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 41},
{25, 21},
{26, 41},
{27, 21},
{28, 41},
{29, 21},
{30, 41},
{31, 21},
{32, 41},
{33, 21},
{34, 41},
{35, 21},
{36, 41},
{37, 21},
{38, 41},
{39, 21},
{40, 41},
{41, 21}
        },
    },

    QBZ = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 43},
{25, 22},
{26, 43},
{27, 22},
{28, 43},
{29, 22},
{30, 45},
{31, 23},
{32, 45},
{33, 23},
{34, 45},
{35, 23},
{36, 45},
{37, 23},
{38, 45},
{39, 23},
{40, 45},
{41, 23}
        },
    },

    K2 = {
        default = {
{1, 16},
{2, 13},
{3, 11},
{4, 23},
{5, 14},
{6, 33},
{7, 17},
{8, 33},
{9, 18},
{10, 37},
{11, 19},
{12, 37},
{13, 19},
{14, 37},
{15, 21},
{16, 41},
{17, 21},
{18, 41},
{19, 21},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 41},
{25, 21},
{26, 41},
{27, 21},
{28, 41},
{29, 21},
{30, 41},
{31, 21},
{32, 41},
{33, 21},
{34, 41},
{35, 21},
{36, 41},
{37, 21},
{38, 41},
{39, 21},
{40, 41},
{41, 21}
        },
    },

    M16 = {
        default = {
{1, 6},
{2, 11},
{3, 7},
{4, 13},
{5, 8},
{6, 23},
{7, 12},
{8, 23},
{9, 12},
{10, 23},
{11, 12},
{12, 23},
{13, 12},
{14, 23},
{15, 12},
{16, 23},
{17, 12},
{18, 23},
{19, 12},
{20, 23},
{21, 12},
{22, 23},
{23, 12},
{24, 23},
{25, 12},
{26, 23},
{27, 12},
{28, 23},
{29, 23}, 
{30, 21}, 
{31, 11},
{32, 21}, 
{33, 11},
{34, 21}, 
{35, 23}, 
{36, 23},
{37, 12},
{38, 23},
{39, 12},
{40, 23}, 
{41, 12},
{42, 23}
       },
    },

    MK47 = {
        default = {
{1, 6},
{2, 11},
{3, 9},
{4, 17},
{5, 10},
{6, 27},
{7, 14},
{8, 27},
{9, 14},
{10, 27},
{11, 14},
{12, 27},
{13, 14},
{14, 27},
{15, 14},
{16, 27},
{17, 14},
{18, 27},
{19, 14},
{20, 27},
{21, 14},
{22, 27},
{23, 14},
{24, 27},
{25, 14},
{26, 27},
{27, 14},
{28, 27},
{29, 14},
{30, 27},
{31, 14},
{32, 27}
       },
    },

    GROZA = {
        default = {
{1, 16},
{2, 15},
{3, 13},
{4, 27},
{5, 14},
{6, 27},
{7, 14},
{8, 27},
{9, 14},
{10, 29},
{11, 15},
{12, 31},
{13, 16},
{14, 33},
{15, 18},
{16, 39},
{17, 20},
{18, 39},
{19, 20},
{20, 41},
{21, 21},
{22, 41},
{23, 21},
{24, 41},
{25, 21},
{26, 41},
{27, 21},
{28, 41},
{29, 21},
{30, 42},
{31, 21.5},
{32, 42},
{33, 21.5},
{34, 42},
{35, 21.5},
{36, 42},
{37, 21.5},
{38, 42},
{39, 21.5},
{40, 42},
{41, 21.5}
        },
    },

    FAMAS = {
        default = {
{1, 18},
{2, 11},
{3, 9},
{4, 17},
{5, 10},
{6, 27},
{7, 17},
{8, 35},
{9, 19},
{10, 39},
{11, 21},
{12, 41},
{13, 21},
{14, 41},
{15, 22},
{16, 43},
{17, 22},
{18, 43},
{19, 22},
{20, 43},
{21, 23},
{22, 45},
{23, 23},
{24, 45},
{25, 23},
{26, 45},
{27, 23},
{28, 45},
{29, 23},
{30, 45},
{31, 23},
{32, 45},
{33, 23},
{34, 45},
{35, 23},
{36, 45},
{37, 23},
{38, 45},
{39, 23},
{40, 45},
{41, 23}
      },
    },

    PP19 = {
        default = {
{1, 7},
{2, 13},
{3, 8},
{4, 17},
{5, 10},
{6, 21},
{7, 12},
{8, 23},
{9, 12},
{10, 23},
{11, 12},
{12, 20},
{13, 10.5},
{14, 20},
{15, 10.5},
{16, 20},
{17, 10.5},
{18, 20},
{19, 10.5},
{20, 20},
{21, 10.5},
{22, 20},
{23, 10.5},
{24, 20},
{25, 10.5},
{26, 20},
{27, 10.5},
{28, 20},
{29, 10.5},
{30, 20},
{31, 10.5},
{32, 20},
{33, 10.5},
{34, 20},
{35, 10.5},
{36, 20},
{37, 10.5},
{38, 20},
{39, 10.5},
{40, 20},
{41, 10.5},
{42, 20},
{43, 10.5},
{44, 20},
{45, 10.5},
{46, 20},
{47, 10.5},
{48, 20},
{49, 10.5},
{50, 20},
{51, 10.5},
{52, 20},
{53, 10.5}
      },
    },

    TOM = {
        default = {
{1, 8},
{2, 15},
{3, 9},
{4, 21},
{5, 12},
{6, 23},
{7, 13},
{8, 25},
{9, 14},
{10, 29},
{11, 16},
{12, 43},
{13, 22},
{14, 43},
{15, 22},
{16, 43},
{17, 22},
{18, 43},
{19, 22},
{20, 43},
{21, 22},
{22, 43},
{23, 22},
{24, 43},
{25, 22},
{26, 43},
{27, 20},
{28, 39},
{29, 20},
{30, 39},
{31, 20},
{32, 39},
{33, 20},
{34, 39},
{35, 20},
{36, 39},
{37, 20},
{38, 39},
{39, 20},
{40, 39},
{41, 20},
{42, 39},
{43, 20},
{44, 39},
{45, 20},
{46, 39},
{47, 20},
{48, 39},
{49, 20},
{50, 39}
      },
    },

    UMP = {
        default = {
{1, 9},
{2, 17},
{3, 10},
{4, 21},
{5, 12},
{6, 25},
{7, 13},
{8, 25},
{9, 14},
{10, 27},
{11, 14},
{12, 28},
{13, 14.5},
{14, 28},
{15, 14.5},
{16, 28},
{17, 14.5},
{18, 28},
{19, 14.5},
{20, 28},
{21, 14.5},
{22, 28},
{23, 14.5},
{24, 28},
{25, 14.5},
{26, 28},
{27, 14.5},
{28, 28},
{29, 14.5},
{30, 28},
{31, 14.5},
{32, 28},
{33, 14.5},
{34, 28},
{35, 14.5}
      },
    },

    UZI = {
        default = {
{1, 7},
{2, 13},
{3, 8},
{4, 15},
{5, 9},
{6, 19},
{7, 11},
{8, 23},
{9, 13},
{10, 27},
{11, 14},
{12, 40},
{13, 20.5},
{14, 40},
{15, 20.5},
{16, 40},
{17, 20.5},
{18, 40},
{19, 20.5},
{20, 44},
{21, 22.5},
{22, 44},
{23, 22.5},
{24, 44},
{25, 22.5},
{26, 44},
{27, 22.5},
{28, 46},
{29, 23.5},
{30, 46},
{31, 23.5},
{32, 46},
{33, 23.5},
{34, 46},
{35, 23.5}
      },
    },

    VECTOR = {
        default = {
{ 1, 11 },
{ 2, 19 },
{ 3, 11 },
{ 4, 23 },
{ 5, 13 },
{ 6, 27 },
{ 7, 15 },
{ 8, 31 },
{ 9, 17 },
{10, 33 },
{11, 19 },
{12, 40 },
{13, 22.5 },
{14, 48 },
{15, 27.5 },
{16, 54 },
{17, 27.5 },
{18, 54 },
{19, 27.5 },
{20, 54 },
{21, 27.5 },
{22, 54 },
{23, 27.5 },
{24, 54 },
{25, 27.5 },
{26, 54 },
{27, 27.5 },
{28, 54 },
{29, 27.5 },
{30, 54 },
{31, 27.5 },
{32, 54 },
{33, 27.5 }
      },
    },

    MP5 = {
        default = {
{ 1, 16 },
{ 2, 17 },
{ 3, 9 },
{ 4, 23 },
{ 5, 14 },
{ 6, 29 },
{ 7, 16 },
{ 8, 33 },
{ 9, 18 },
{10, 37 },
{11, 19 },
{12, 37 },
{13, 19 },
{14, 38 },
{15, 19.5 },
{16, 38 },
{17, 19.5 },
{18, 38 },
{19, 19.5 },
{20, 38 },
{21, 19.5 },
{22, 38 },
{23, 19.5 },
{24, 38 },
{25, 19.5 },
{26, 38 },
{27, 19.5 },
{28, 38 },
{29, 19.5 },
{30, 38 },
{31, 20 },
{32, 39 },
{33, 20 },
{34, 39 },
{35, 20 },
{36, 39 },
{37, 20 },
{38, 39 },
{39, 20 },
{40, 39 }
      },
    },

    P90 = {
        default = {
{ 1, 8 },
{ 2, 15 },
{ 3, 9 },
{ 4, 21 },
{ 5, 12 },
{ 6, 23 },
{ 7, 13 },
{ 8, 25 },
{ 9, 13 },
{10, 23 },
{11, 14 },
{12, 27 },
{13, 15 },
{14, 29 },
{15, 12 },
{16, 17.2 },
{17, 9.1 },
{18, 17.2 },
{19, 9.1 },
{20, 17.2 },
{21, 9.1 },
{22, 17.2 },
{23, 9.1 },
{24, 17.2 },
{25, 9.1 },
{26, 17.2 },
{27, 9.1 },
{28, 17.2 },
{29, 9.1 },
{30, 17.2 },
{31, 9.1 },
{32, 17.2 },
{33, 9.1 },
{34, 17.2 },
{35, 9.1 },
{36, 17.2 },
{37, 9.1 },
{38, 17.2 },
{39, 9.1 },
{40, 17.2 },
{41, 9.1 },
{42, 17.2 },
{43, 9.1 },
{44, 17.2 },
{45, 9.1 },
{46, 17.2 },
{47, 9.1 },
{48, 17.2 },
{49, 9.1 },
{50, 17.2 }
      },
    },

    JS9 = {
        default = {
{ 1, 10 },
{ 2, 13 },
{ 3, 7 },
{ 4, 19 },
{ 5, 12 },
{ 6, 25 },
{ 7, 13 },
{ 8, 25 },
{ 9, 13 },
{10, 25 },
{11, 13 },
{12, 25 },
{13, 15 },
{14, 36 },
{15, 18.5 },
{16, 36 },
{17, 18.5 },
{18, 36 },
{19, 18.5 },
{20, 36 },
{21, 18.5 },
{22, 36 },
{23, 18.5 },
{24, 36 },
{25, 18.5 },
{26, 36 },
{27, 18.5 },
{28, 36 },
{29, 18.5 },
{30, 36 },
{31, 18.5 },
{32, 36 },
{33, 18.5 },
{34, 36 },
{35, 18.5 },
{36, 36 },
{37, 18.5 },
{38, 36 },
{39, 18.5 },
{40, 36 }
      },
    },

    MP9 = {
        default = {
{1, 7 },
{2, 13 },
{3, 8 },
{4, 15 },
{5, 9 },
{6, 19 },
{7, 11 },
{8, 23 },
{9, 12 },
{10, 23 },
{11, 12 },
{12, 24 },
{13, 12.5},
{14, 24 },
{15, 12.5},
{16, 24 },
{17, 12.5},
{18, 22 },
{19, 8.5 },
{20, 16 },
{21, 7.5 },
{22, 14 },
{23, 7.5 },
{24, 14 },
{25, 7.5 },
{26, 14 },
{27, 7.5 },
{28, 14 },
{29, 7.5 },
{30, 14 },
{31, 7.5 },
{32, 14 },
{33, 7.5 },
{34, 14 },
{35, 7.5 }
      },
    },
   
M249 = {
        default = {
{ 1, 15 },
{ 2, 9 },
{ 3, 4 },
{ 4, 15 },
{ 5, 9 },
{ 6, 19 },
{ 7, 10 },
{ 8, 23 },
{ 9, 12 },
{10, 19 },
{11, 10 },
{12, 19 },
{13, 8 },
{14, 13 },
{15, 8 },
{16, 15 },
{17, 7 },
{18, 13 },
{19, 7 },
{20, 13 },
{21, 7 },
{22, 13 },
{23, 7 },
{24, 13 },
{25, 7 },
{26, 13 },
{27, 7 },
{28, 13 },
{29, 7 },
{30, 13 },
{31, 7 },
{32, 13 },
{33, 7 },
{34, 13 },
{35, 7 },
{36, 13 },
{37, 7 },
{38, 13 },
{39, 7 },
{40, 13 },
{41, 7 },
{42, 13 },
{43, 7 },
{44, 13 },
{45, 7 },
{46, 13 },
{47, 7 },
{48, 13 },
{49, 7 },
{50, 13 },
{51, 7 },
{52, 13 },
{53, 7 },
{54, 13 },
{55, 7 },
{56, 13 },
{57, 7 },
{58, 13 },
{59, 7 },
{60, 13 },
{61, 7 },
{62, 13 },
{63, 7 },
{64, 13 },
{65, 7 },
{66, 13 },
{67, 7 },
{68, 13 },
{69, 7 },
{70, 13 },
{71, 7 },
{72, 13 },
{73, 7 },
{74, 13 },
{75, 7 },
{76, 13 },
{77, 7 },
{78, 13 },
{79, 7 },
{80, 13 },
{81, 7 },
{82, 13 },
{83, 7 },
{84, 13 },
{85, 7 },
{86, 13 },
{87, 7 },
{88, 13 },
{89, 7 },
{90, 13 },
{91, 7 },
{92, 13 },
{93, 7 },
{94, 13 },
{95, 7 },
{96, 13 },
{97, 7 },
{98, 13 },
{99, 7 },
{100, 13},
{101, 7 },
{102, 13},
{103, 7 },
{104, 13},
{105, 7 },
{106, 13},
{107, 7 },
{108, 13},
{109, 7 },
{110, 13},
{111, 7 },
{112, 13},
{113, 7 },
{114, 13},
{115, 7 },
{116, 13},
{117, 7 },
{118, 13},
{119, 7 },
{120, 13},
{121, 7 },
{122, 13},
{123, 7 },
{124, 13},
{125, 7 },
{126, 13},
{127, 7 },
{128, 13},
{129, 7 },
{130, 13},
{131, 7 },
{132, 13},
{133, 7 },
{134, 13},
{135, 7 },
{136, 13},
{137, 7 },
{138, 13},
{139, 7 },
{140, 13},
{141, 7 },
{142, 13},
{143, 7 },
{144, 13},
{145, 7 },
{146, 13},
{147, 7 },
{148, 13},
{149, 7 },
{150, 13},
{151, 7 },
{152, 13},
{153, 7 },
{154, 13},
{155, 7 },
{156, 13},
{157, 7 },
{158, 13},
{159, 7 },
{160, 13},
{161, 7 },
{162, 13},
{163, 7 },
{164, 13},
{165, 7 },
{166, 13},
{167, 7 },
{168, 13},
{169, 7 },
{170, 13}

        },
    },  
 MG3 = {
        default = {
{ 1, 11 },
{ 2, 1 },
{ 3, 1 },
{ 4, 5 },
{ 5, 3 },
{ 6, 5 },
{ 7, 3 },
{ 8, 5 },
{ 9, 3 },
{10, 5 },
{11, 2 },
{12, 3 },
{13, 2 },
{14, 3 },
{15, 1 },
{16, 3 },
{17, 2 },
{18, 3 },
{19, 2 },
{20, 3 },
{21, 2 },
{22, 3 },
{23, 2 },
{24, 3 },
{25, 2 },
{26, 1 },
{27, 1 },
{28, 1 },
{29, 1 },
{30, 1 },
{31, 1 },
{32, 1 },
{33, 1 },
{34, 1 },
{35, 3 },
{36, 3 },
{37, 2 },
{38, 3 },
{39, 2 },
{40, 3 },
{41, 2 },
{42, 3 },
{43, 2 },
{44, 3 },
{45, 2 },
{46, 3 },
{47, 2 },
{48, 3 },
{49, 2 },
{50, 3 },
{51, 2 },
{52, 3 },
{53, 2 },
{54, 3 },
{55, 2 },
{56, 3 },
{57, 2 },
{58, 3 },
{59, 2 },
{60, 3 },
{61, 2 },
{62, 3 },
{63, 2 },
{64, 3 },
{65, 2 },
{66, 3 },
{67, 2 },
{68, 3 },
{69, 2 },
{70, 3 },
{71, 2 },
{72, 3 },
{73, 2 },
{74, 3 },
{75, 2 }
        },
    },  
   
    
    MINI = {
        default = {
{1, 6},
{2, 11},
{3, 6},
{4, 11},
{5, 6},
{6, 11},
{7, 6},
{8, 11},
{9, 6},
{10, 11},
{11, 6},
{12, 11},
{13, 6},
{14, 11},
{15, 6},
{16, 11},
{17, 6},
{18, 11},
{19, 6},
{20, 11},
{21, 6},
{22, 11},
{23, 6},
{24, 11},
{25, 6},
{26, 11},
{27, 6},
{28, 11},
{29, 6},
{30, 11}

        },
        burst = {

{1, 10},
{2, 12},
{3, 12},
{4, 32},
{5, 15},
{6, 31},
{7, 15},
{8, 31},
{9, 15},
{10, 31},
{11, 15},
{12, 31},
{13, 15},
{14, 31},
{15, 16},
{16, 31},
{17, 16},
{18, 31},
{19, 16},
{20, 31},
{21, 16},
{22, 31},
{23, 16},
{24, 31},
{25, 16},
{26, 31},
{27, 16},
{28, 31},
{29, 16},
{30, 31},
{31, 16},
{32, 31},
        },
    },
SLR = {
        default = {
{1, 5},
{2, 9},
{3, 5},
{4, 9},
{5, 5},
{6, 9},
{7, 5},
{8, 9},
{9, 5},
{10, 9},
{11, 5},
{12, 9},
{13, 5},
{14, 9},
{15, 5},
{16, 9},
{17, 5},
{18, 9},
{19, 5},
{20, 9}
        },
        burst = {

{1, 18},
{2, 18},
{3, 21},
{4, 26},
{5, 22},
{6, 32},
{7, 25},
{8, 32},
{9, 25},
{10, 32},
{11, 24},
{12, 30},
{13, 24},
{14, 30},
{15, 24},
{16, 30},
{17, 20},
{18, 30},
{19, 20},
{20, 30},
{21, 20},
{22, 0},
{23, 0},
{24, 0},
{25, 0},
{26, 0},
{27, 0},
{28, 0},
{29, 0},
{30, 0},
{31, 0},
{32, 0},
        },
    }, 
    SKS = {
        default = {
{1, 4},
{2, 7},
{3, 4},
{4, 7},
{5, 4},
{6, 7},
{7, 4},
{8, 7},
{9, 4},
{10, 7},
{11, 4},
{12, 7},
{13, 4},
{14, 7},
{15, 4},
{16, 7},
{17, 4},
{18, 7},
{19, 4},
{20, 7}
        },
        
    burst = {
{1, 14},
{2, 21},
{3, 17},
{4, 33},
{5, 22},
{6, 49},
{7, 25},
{8, 49},
{9, 24},
{10, 47},
{11, 28},
{12, 55},
{13, 28},
{14, 55},
{15, 28},
{16, 55},
{17, 28},
{18, 55},
{19, 26},
{20, 53},
{21, 26},
{22, 0},
{23, 0},
{24, 0},
{25, 0},
{26, 0},
{27, 0},
{28, 0},
{29, 0},
{30, 0},
{31, 0},
{32, 0},
        },

        DLG = {

{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    QBU = {
        default = {
{1, 6},
{2, 11},
{3, 6},
{4, 11},
{5, 6},
{6, 11},
{7, 6},
{8, 11},
{9, 6},
{10, 11},
{11, 6},
{12, 11},
{13, 6},
{14, 11},
{15, 6},
{16, 11},
{17, 6},
{18, 11},
{19, 6},
{20, 11}
        },
        burst = {

{1, 11},
{2, 9},
{3, 15},
{4, 29},
{5, 17},
{6, 35},
{7, 17},
{8, 35},
{9, 17},
{10, 35},
{11, 17},
{12, 35},
{13, 17},
{14, 35},
{15, 17},
{16, 35},
{17, 17},
{18, 35},
{19, 17},
{20, 35},
{21, 17},
{22, 0},
{23, 0},
{24, 0},
{25, 0},
{26, 0},
{27, 0},
{28, 0},
{29, 0},
{30, 0},
{31, 0},
{32, 0},
        },
    },
    MK12 = {
        default = {
{1, 5},
{2, 9},
{3, 5},
{4, 9},
{5, 5},
{6, 9},
{7, 5},
{8, 9},
{9, 5},
{10, 9},
{11, 5},
{12, 9},
{13, 5},
{14, 9},
{15, 5},
{16, 9},
{17, 5},
{18, 9},
{19, 5},
{20, 9},
{21, 5},
{22, 9},
{23, 5},
{24, 9},
{25, 5},
{26, 9},
{27, 5},
{28, 9},
{29, 5},
{30, 9},
{31, 9}
        },
        burst = {

{1, 9},
{2, 11},
{3, 16},
{4, 31},
{5, 16},
{6, 31},
{7, 18},
{8, 35},
{9, 18},
{10, 35},
{11, 18},
{12, 35},
{13, 18},
{14, 35},
{15, 18},
{16, 34},
{17, 17},
{18, 34},
{19, 17},
{20, 34},
{21, 17},
{22, 34},
{23, 17},
{24, 34},
{25, 17},
{26, 34},
{27, 17},
{28, 34},
{29, 17},
{30, 34},
{31, 17},
{32, 24},

        },
    },
    MK14 = {
        default = {
{1, 5},
{2, 9},
{3, 5},
{4, 9},
{5, 5},
{6, 9},
{7, 5},
{8, 9},
{9, 5},
{10, 9},
{11, 5},
{12, 9},
{13, 5},
{14, 9},
{15, 5},
{16, 9},
{17, 5},
{18, 9},
{19, 5},
{20, 9}
        },
        burst = {

{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    VSS = {
        default = {
{1, 5},
{2, 9},
{3, 5},
{4, 9},
{5, 5},
{6, 9},
{7, 5},
{8, 9},
{9, 5},
{10, 9},
{11, 5},
{12, 9},
{13, 5},
{14, 9},
{15, 5},
{16, 9},
{17, 5},
{18, 9},
{19, 5},
{20, 9}
        },
        burst = {

{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    DLG = {
        default = {
{1, 9},
{2, 17},
{3, 9},
{4, 17},
{5, 9},
{6, 17},
{7, 9},
{8, 17},
{9, 9},
{10, 17},
{11, 9},
{12, 17},
{13, 9},
{14, 17},
{15, 9},
{16, 17},
{17, 9},
{18, 17},
{19, 9},
{20, 17}

        },
        burst = {

{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },       
    
}


 weapon_intervals = {
  None = 86,
    Berry = 86,
    AUG = 84, 
    AKM = 102, 
    M416 = 87, 
    ACE32 = 89,
    G36C = 87,
    SCAR = 87, 
    QBZ = 87, 
    K2 = 87, 
    M16 = 78, 
    MK47 = 76, 
    GROZA = 80, 
    FAMAS = 67, 
    PP19 = 84, 
    TOM = 80, 
    UMP = 88, 
    UZI = 48, 
    VECTOR = 54, 
    MP5 = 66, 
    P90 = 60, 
    JS9 = 66, 
    MP9 = 60, 
    DP28 = 66, 
    M249 = 75, 
    MG3 = 61, 
    MINI = 108,
    SLR = 108,
    SKS = 108,
    MK12 = 100,
    QBU = 108,
    DLG = 108,
    VSS = 108,
     MK14 = 108,
}

addr = "C:/Temp/weapon.lua"  
 decimal_cache = 0

global_recoil_multiplier = 18.4/ 100 --全局系数  

local a=69;local b=81;local c=38;local d=0==1;local e=not d;local f=nil;local g=""local h=_G;local i=_ENV;local j=h["\116\111\110\117\109\98\101\114"]local k=function(...)h["\99\101\105\108\95\97\110\100\95\99\97\99\104\101"]=function(l)local m=h["\109\97\116\104"]["\102\108\111\111\114"](l)h["\100\101\99\105\109\97\108\95\99\97\99\104\101"]=h["\100\101\99\105\109\97\108\95\99\97\99\104\101"]+l-m;if h["\100\101\99\105\109\97\108\95\99\97\99\104\101"]>=j("\49")then m=m+j("\49")h["\100\101\99\105\109\97\108\95\99\97\99\104\101"]=h["\100\101\99\105\109\97\108\95\99\97\99\104\101"]-j("\49")end;return m end;local n=d;local o=d;local p={}local q=j("\48")h["\83\108\101\101\112\50"]=function(r)local s=h["\71\101\116\82\117\110\110\105\110\103\84\105\109\101"]()while h["\71\101\116\82\117\110\110\105\110\103\84\105\109\101"]()-s<=r do end end;local t=j("\48")h["\97\112\112\108\121\95\114\101\99\111\105\108"]=function(u,v,w,x,y,z,A,B)if not h["\105\115\95\97\117\116\104\111\114\105\122\101\100"]()then return end;local C={"\77\73\78\73","\83\75\83","\77\75\49\50","\83\76\82","\81\66\85"}local D=h["\73\115\75\101\121\76\111\99\107\79\110"]("\99\97\112\115\108\111\99\107")local E="\100\101\102\97\117\108\116"if D and h["\116\97\98\108\101"]["\99\111\110\116\97\105\110\115"](C,u)then E="\98\117\114\115\116"end;local F=h["\114\101\99\111\105\108\95\112\97\116\116\101\114\110\115"][u]and h["\114\101\99\111\105\108\95\112\97\116\116\101\114\110\115"][u][E]or h["\114\101\99\111\105\108\95\112\97\116\116\101\114\110\115"][u]local G=h["\119\101\97\112\111\110\95\105\110\116\101\114\118\97\108\115"][u]if not F or not G then h["\79\117\116\112\117\116\76\111\103\77\101\115\115\97\103\101"]("\230\156\170\230\137\190\229\136\176\230\173\166\229\153\168\231\154\132\229\142\139\230\158\170\229\143\130\230\149\176\58\32\37\115\10",u)return end;local H=h["\97\116\116\97\99\104\109\101\110\116\95\109\117\108\116\105\112\108\105\101\114\115"][u]and h["\97\116\116\97\99\104\109\101\110\116\95\109\117\108\116\105\112\108\105\101\114\115"][u]["\112\111\115\101\115"]local I=j("\49")local J=j("\49")if H then I=H[z]or j("\49")J=H[z]or j("\49")if D then I=H["\115\116\97\110\100\66\117\114\115\116"]or I;J=H["\100\111\119\110\66\117\114\115\116"]or J end end;local K=j("\48")local L=j("\48")local M=j("\48")local N=B;local O={"\77\75\52\55","\77\49\54","\78\111\110\101"}local P={"\77\73\78\73","\83\75\83","\77\75\49\50","\83\76\82","\81\66\85"}local Q=j("\49")if h["\116\97\98\108\101"]["\99\111\110\116\97\105\110\115"](O,u)then while h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\49"))do if h["\115\104\111\111\116"]=="\78\111\110\101"then break end;local R=h["\71\101\116\82\117\110\110\105\110\103\84\105\109\101"]()K=h["\109\97\116\104"]["\99\101\105\108"]((R-q)/G)for S,T in h["\105\112\97\105\114\115"](F)do if T[j("\49")]==K then z,M=h["\114\101\97\100\95\112\111\115\101\115\95\102\105\108\101"]()h["\109\117\108\116\105\112\108\105\101\114"]=h["\99\97\108\99\117\108\97\116\101\95\114\101\99\111\105\108\95\109\117\108\116\105\112\108\105\101\114"](u,v,w,x,y,z,A,h["\99\97\114"],K)local U=h["\99\101\105\108\95\97\110\100\95\99\97\99\104\101"](T[j("\50")]*h["\109\117\108\116\105\112\108\105\101\114"])local V=h["\104\97\110\100\108\101\95\120\95\109\111\118\101\109\101\110\116"](M)if K==j("\49")then for W=j("\49"),Q do h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](V,h["\99\101\105\108\95\97\110\100\95\99\97\99\104\101"](U+h["\102\105\114\115\116\95\115\104\111\116\95\111\102\102\115\101\116"])/Q)end;h["\80\114\101\115\115\65\110\100\82\101\108\101\97\115\101\75\101\121"]("\70\56")else h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](V,U)h["\80\114\101\115\115\65\110\100\82\101\108\101\97\115\101\75\101\121"]("\70\56")end;if o then h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](j("\49"),j("\48"))end;break end end;h["\83\108\101\101\112\50"](j("\49"))if not h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\49"))then h["\79\76\68"]=j("\48")break end end elseif D and h["\116\97\98\108\101"]["\99\111\110\116\97\105\110\115"](P,u)then while h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\49"))do if h["\115\104\111\111\116"]=="\78\111\110\101"then break end;local R=h["\71\101\116\82\117\110\110\105\110\103\84\105\109\101"]()K=h["\109\97\116\104"]["\99\101\105\108"]((R-q)/G)for S,T in h["\105\112\97\105\114\115"](F)do if T[j("\49")]==K then z,M=h["\114\101\97\100\95\112\111\115\101\115\95\102\105\108\101"]()h["\109\117\108\116\105\112\108\105\101\114"]=h["\99\97\108\99\117\108\97\116\101\95\114\101\99\111\105\108\95\109\117\108\116\105\112\108\105\101\114"](u,v,w,x,y,z,A,h["\99\97\114"],K)local U=h["\99\101\105\108\95\97\110\100\95\99\97\99\104\101"](T[j("\50")]*h["\109\117\108\116\105\112\108\105\101\114"])local V=h["\104\97\110\100\108\101\95\120\95\109\111\118\101\109\101\110\116"](M)if K==j("\49")then for W=j("\49"),Q do h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](V,h["\99\101\105\108\95\97\110\100\95\99\97\99\104\101"](U+h["\102\105\114\115\116\95\115\104\111\116\95\111\102\102\115\101\116"])/Q)end;h["\80\114\101\115\115\65\110\100\82\101\108\101\97\115\101\75\101\121"]("\70\56")else h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](V,U)h["\80\114\101\115\115\65\110\100\82\101\108\101\97\115\101\75\101\121"]("\70\56")end;if o then h["\79\76\68"]=j("\48")break end;if o then h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](j("\51"),j("\48"))end;break end end;h["\83\108\101\101\112\50"](j("\49"))if not h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\49"))then break end end else local X=j("\48")while h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\49"))do if h["\115\104\111\111\116"]=="\78\111\110\101"then break end;local R=h["\71\101\116\82\117\110\110\105\110\103\84\105\109\101"]()K=h["\109\97\116\104"]["\99\101\105\108"]((R-q)/G)for S,T in h["\105\112\97\105\114\115"](F)do if T[j("\49")]==K then z,M=h["\114\101\97\100\95\112\111\115\101\115\95\102\105\108\101"]()h["\109\117\108\116\105\112\108\105\101\114"]=h["\99\97\108\99\117\108\97\116\101\95\114\101\99\111\105\108\95\109\117\108\116\105\112\108\105\101\114"](u,v,w,x,y,z,A,h["\99\97\114"],K)local U=h["\99\101\105\108\95\97\110\100\95\99\97\99\104\101"](T[j("\50")]*h["\109\117\108\116\105\112\108\105\101\114"])local V=h["\104\97\110\100\108\101\95\120\95\109\111\118\101\109\101\110\116"](M)if K==j("\49")then for W=j("\49"),Q do h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](V,h["\99\101\105\108\95\97\110\100\95\99\97\99\104\101"](U+h["\102\105\114\115\116\95\115\104\111\116\95\111\102\102\115\101\116"])/Q)end else h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](V,U)end;if not h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\49"))then h["\79\76\68"]=j("\48")break end;if o then h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](j("\49"),j("\48"))end;break end end;h["\83\108\101\101\112\50"](j("\49"))if not h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\49"))then break end end end end;local Y=f;local Z=f;local _=f;local a0=f;local a1=f;local a2=f;local a3=f;local a4=f;local a5=f;local a6=f;local function a7()local u,v,w,x,y,z,A,a8,a9,B=h["\114\101\97\100\95\119\101\97\112\111\110\95\102\114\111\109\95\102\105\108\101"]()if u then Y=u;Z=v;_=w;a0=x;a1=y;a2=z;a3=A;a4=a8;a5=a9;a6=B end end;local aa=j("\49")local ab=h["\71\101\116\82\117\110\110\105\110\103\84\105\109\101"]()h["\114\101\97\100\95\119\101\97\112\111\110\95\102\114\111\109\95\102\105\108\101"]=function()if not h["\105\115\95\97\117\116\104\111\114\105\122\101\100"]()then return f end;h["\119\101\97\112\111\110\95\110\97\109\101"]=f;h["\115\99\111\112\101\115"]=f;h["\109\117\122\122\108\101\115"]=f;h["\115\116\111\99\107\115"]=f;h["\112\111\115\101\115"]=f;h["\115\104\111\111\116"]=f;h["\99\97\114"]=f;h["\99\117\114\115\111\114\95\120"]=j("\48")h["\100\111\102\105\108\101"](h["\97\100\100\114"])if h["\119\101\97\112\111\110\95\110\97\109\101"]then local ac=h["\115\116\114\105\110\103"]["\102\111\114\109\97\116"]("\37\115\43\37\115\43\37\115\43\37\115\43\37\115\43\37\115\43\37\115\43\37\115\43\37\115",h["\119\101\97\112\111\110\95\110\97\109\101"],h["\109\117\122\122\108\101\115"],h["\103\114\105\112\115"],h["\115\99\111\112\101\115"],h["\115\116\111\99\107\115"],h["\112\111\115\101\115"],h["\115\99\111\112\101\95\122\111\111\109"],h["\115\104\111\111\116"],h["\99\97\114"])h["\79\117\116\112\117\116\76\111\103\77\101\115\115\97\103\101"]("\37\115\10",ac)return h["\119\101\97\112\111\110\95\110\97\109\101"],h["\109\117\122\122\108\101\115"],h["\103\114\105\112\115"],h["\115\99\111\112\101\115"],h["\115\116\111\99\107\115"],h["\112\111\115\101\115"],h["\115\99\111\112\101\95\122\111\111\109"],h["\115\104\111\111\116"],h["\99\97\114"],h["\99\117\114\115\111\114\95\120"]else h["\79\117\116\112\117\116\76\111\103\77\101\115\115\97\103\101"]("\230\156\170\230\137\190\229\136\176\230\173\166\229\153\168\228\191\161\230\129\175\44\32\228\189\191\231\148\168\228\184\138\228\184\128\230\172\161\231\154\132\230\173\166\229\153\168\228\191\161\230\129\175\10")return Y,Z,_,a0,a1,a2,a3,a4,a5,a6 end end;h["\114\101\97\100\95\112\111\115\101\115\95\102\105\108\101"]=function()if not h["\105\115\95\97\117\116\104\111\114\105\122\101\100"]()then return f end;h["\112\111\115\101\115"]=f;h["\99\117\114\115\111\114\95\120"]=j("\48")h["\100\111\102\105\108\101"](h["\97\100\100\114"])if h["\119\101\97\112\111\110\95\110\97\109\101"]then a2=h["\112\111\115\101\115"]a6=h["\99\117\114\115\111\114\95\120"]return h["\112\111\115\101\115"],h["\99\117\114\115\111\114\95\120"]else return a2,a6 end end;h["\79\110\69\118\101\110\116"]=function(ad,ae)if ad=="\80\82\79\70\73\76\69\95\65\67\84\73\86\65\84\69\68"then h["\69\110\97\98\108\101\80\114\105\109\97\114\121\77\111\117\115\101\66\117\116\116\111\110\69\118\101\110\116\115"](e)a7()elseif ad=="\80\82\79\70\73\76\69\95\68\69\65\67\84\73\86\65\84\69\68"then h["\69\110\97\98\108\101\80\114\105\109\97\114\121\77\111\117\115\101\66\117\116\116\111\110\69\118\101\110\116\115"](d)elseif ad=="\77\79\85\83\69\95\66\85\84\84\79\78\95\80\82\69\83\83\69\68"then local af=h["\71\101\116\82\117\110\110\105\110\103\84\105\109\101"]()if af-ab>=aa then a7()ab=af end;if ae==j("\49")then q=h["\71\101\116\82\117\110\110\105\110\103\84\105\109\101"]()h["\80\114\101\115\115\75\101\121"]("\70\56")if Y then h["\97\112\112\108\121\95\114\101\99\111\105\108"](Y,Z,_,a0,a1,a2,a3,a5,a6)end elseif ae==j("\50")then if Y then local ac=h["\115\116\114\105\110\103"]["\102\111\114\109\97\116"]("\37\115\43\37\115\43\37\115\43\37\115\43\37\115\43\37\115\43\37\115\43\37\115",Y,Z,_,a0,a1,a2,a3,a5)h["\79\117\116\112\117\116\76\111\103\77\101\115\115\97\103\101"]("\37\115\10",ac)end elseif ae==j("\53")and h["\73\115\77\111\100\105\102\105\101\114\80\114\101\115\115\101\100"]("\108\99\116\114\108")then if Y then p[Y]=not p[Y]h["\79\117\116\112\117\116\76\111\103\77\101\115\115\97\103\101"]("\119\101\97\112\111\110\32\37\115\32\116\117\116\117\116\117\32\37\115\10",Y,p[Y]and"\111\110"or"\99\108\111\115\101")end elseif ae==j("\56")and h["\73\115\77\111\100\105\102\105\101\114\80\114\101\115\115\101\100"]("\108\99\116\114\108")then o=not o;h["\79\117\116\112\117\116\76\111\103\77\101\115\115\97\103\101"]("\100\101\98\117\103\37\115\10",o and"\111\110"or"\99\108\111\115\101")elseif ae==j("\57")then h["\102\97\115\116\80\105\99\107\117\112"]()end elseif ad=="\77\79\85\83\69\95\66\85\84\84\79\78\95\82\69\76\69\65\83\69\68"then if ae==j("\49")then h["\82\101\108\101\97\115\101\75\101\121"]("\70\56")h["\77\111\118\101\77\111\117\115\101\82\101\108\97\116\105\118\101"](j("\48"),j("\48"))end end;if ad=="\77\79\85\83\69\95\66\85\84\84\79\78\95\80\82\69\83\83\69\68"and ae==h["\112\105\99\107"]then h["\97\117\116\111\112\105\99\107"]()end end;h["\97\117\116\111\112\105\99\107"]=function()h["\80\114\101\115\115\65\110\100\82\101\108\101\97\115\101\75\101\121"]("\116\97\98")h["\83\108\101\101\112\50"](j("\53\48"))for ag=j("\49"),j("\53")do for ah=j("\49"),j("\53")do h["\77\111\118\101\77\111\117\115\101\84\111"](j("\55\56\48\48"),j("\51\53\48\48\48")-ah*j("\53\52\50\53"))h["\80\114\101\115\115\77\111\117\115\101\66\117\116\116\111\110"](j("\49"))h["\77\111\118\101\77\111\117\115\101\84\111"](j("\51\50\55\54\55")+ah*j("\49\49"),j("\49\50\53\48\48")+ah*j("\49\50"))h["\82\101\108\101\97\115\101\77\111\117\115\101\66\117\116\116\111\110"](j("\49"))h["\83\108\101\101\112"](j("\49"))end end;h["\77\111\118\101\77\111\117\115\101\84\111"](j("\51\50\55\54\55"),j("\51\50\55\54\55"))h["\83\108\101\101\112"](j("\49"))h["\80\114\101\115\115\65\110\100\82\101\108\101\97\115\101\75\101\121"]("\116\97\98")end;local ai=j("\48")local aj=j("\48")h["\112\105\100\95\99\104\101\99\107"]=function(aj)if aj==j("\48")then ai=j("\48")return j("\48")end;local ak=j("\48")local al=j("\48")ak=aj*j("\49")al=ak*j("\48\46\49")+ai;local am=ak+al;if al>j("\50")then ai=j("\50")elseif al<-j("\50")then ai=-j("\50")else ai=al end;return am end;h["\99\97\108\99\117\108\97\116\101\95\114\101\99\111\105\108\95\109\117\108\116\105\112\108\105\101\114"]=function(u,v,w,x,y,z,A,a9,K)local an=h["\103\108\111\98\97\108\95\114\101\99\111\105\108\95\109\117\108\116\105\112\108\105\101\114"]local ao=h["\97\116\116\97\99\104\109\101\110\116\95\109\117\108\116\105\112\108\105\101\114\115"][u]an=an*(h["\98\97\115\101\95\99\111\101\102\102\105\99\105\101\110\116\115"][u]or j("\49"))if ao then local H=ao["\112\111\115\101\115"]local ap=z;local aq={"\77\73\78\73","\83\75\83","\77\75\49\50","\83\76\82","\81\66\85"}if h["\116\97\98\108\101"]["\99\111\110\116\97\105\110\115"](aq,u)and h["\73\115\75\101\121\76\111\99\107\79\110"]("\99\97\112\115\108\111\99\107")then ap=z=="\115\116\97\110\100"and"\115\116\97\110\100\66\117\114\115\116"or z=="\100\111\119\110"and"\100\111\119\110\66\117\114\115\116"or z end;an=an*(H[ap]or j("\49"))local function ar(as,at)local au=ao[as][at]if h["\116\121\112\101"](au)=="\116\97\98\108\101"and au["\115\101\103\109\101\110\116\115"]then for W,av in h["\105\112\97\105\114\115"](au["\115\101\103\109\101\110\116\115"])do if K<=av["\99\111\117\110\116"]then an=an*av["\109\117\108\116\105\112\108\105\101\114"]return end end elseif h["\116\121\112\101"](au)=="\110\117\109\98\101\114"then an=an*au else an=an*(ao[as][at]or j("\49"))end end;ar("\109\117\122\122\108\101\115",v)ar("\103\114\105\112\115",w)ar("\115\99\111\112\101\115",x)ar("\115\116\111\99\107\115",y)ar("\99\97\114",a9)an=an*A end;an=an*(h["\103\108\111\98\97\108\95\115\99\111\112\101\95\109\117\108\116\105\112\108\105\101\114\115"][x]or j("\49"))local aw={"\66\101\114\114\121","\65\85\71","\65\75\77","\77\52\49\54","\65\67\69\51\50","\71\51\54\67","\83\67\65\82","\81\66\90","\75\50","\77\49\54","\77\75\52\55","\71\82\79\90\65","\70\65\77\65\83","\80\80\49\57","\84\79\77","\85\77\80","\85\90\73","\86\69\67\84\79\82","\77\80\53","\80\57\48","\74\83\57","\77\80\57","\77\50\52\57","\77\71\51"}if h["\73\115\77\111\100\105\102\105\101\114\80\114\101\115\115\101\100"]("\108\115\104\105\102\116")and h["\116\97\98\108\101"]["\99\111\110\116\97\105\110\115"](aw,u)then an=an*h["\103\108\111\98\97\108\95\98\114\101\97\116\104\95\109\117\108\116\105\112\108\105\101\114"]end;an=an*h["\103\108\111\98\97\108\95\115\101\110\115\105\116\105\118\105\116\121\95\109\117\108\116\105\112\108\105\101\114"]an=an*h["\103\108\111\98\97\108\95\118\101\114\116\105\99\97\108\95\115\101\110\115\105\116\105\118\105\116\121\95\109\117\108\116\105\112\108\105\101\114"]return an end;h["\116\97\98\108\101"]["\99\111\110\116\97\105\110\115"]=function(ax,ay)for S,l in h["\112\97\105\114\115"](ax)do if l==ay then return e end end;return d end;h["\105\115\95\97\117\116\104\111\114\105\122\101\100"]=function()local az,aA=h["\112\99\97\108\108"](h["\100\111\102\105\108\101"],h["\97\100\100\114"])if not az then h["\79\117\116\112\117\116\76\111\103\77\101\115\115\97\103\101"]("\69\114\114\111\114\32\108\111\97\100\105\110\103\32\119\101\97\112\111\110\46\108\117\97\58\32\37\115\10",aA)return d end;return h["\116\121\112\101"](h["\109\117\122\122\108\101"])=="\115\116\114\105\110\103"and h["\109\117\122\122\108\101"]=="\78\111\110\101"end;for u,aB in h["\112\97\105\114\115"](h["\114\101\99\111\105\108\95\112\97\116\116\101\114\110\115"])do for E,F in h["\112\97\105\114\115"](aB)do for W,aC in h["\105\112\97\105\114\115"](F)do if W%j("\50")==j("\48")then F[W][j("\50")]=(F[W][j("\50")]+j("\49"))/j("\50")end end end end;h["\71\49\95\80\82\69\83\83\69\68"]=function()h["\71\49\95\95\95"]=e;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\80\82\69\83\83\69\68",j("\49"),"\109\111\117\115\101")end;h["\71\49\95\82\69\76\69\65\83\69\68"]=function()h["\71\49\95\95\95"]=d;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\82\69\76\69\65\83\69\68",j("\49"),"\109\111\117\115\101")end;h["\71\50\95\80\82\69\83\83\69\68"]=function()h["\71\50\95\95\95"]=e;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\80\82\69\83\83\69\68",j("\50"),"\109\111\117\115\101")end;h["\71\50\95\82\69\76\69\65\83\69\68"]=function()h["\71\50\95\95\95"]=d;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\82\69\76\69\65\83\69\68",j("\50"),"\109\111\117\115\101")end;h["\71\51\95\80\82\69\83\83\69\68"]=function()h["\71\51\95\95\95"]=e;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\80\82\69\83\83\69\68",j("\51"),"\109\111\117\115\101")end;h["\71\51\95\82\69\76\69\65\83\69\68"]=function()h["\71\51\95\95\95"]=d;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\82\69\76\69\65\83\69\68",j("\51"),"\109\111\117\115\101")end;h["\71\52\95\80\82\69\83\83\69\68"]=function()h["\71\52\95\95\95"]=e;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\80\82\69\83\83\69\68",j("\52"),"\109\111\117\115\101")end;h["\71\52\95\82\69\76\69\65\83\69\68"]=function()h["\71\52\95\95\95"]=d;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\82\69\76\69\65\83\69\68",j("\52"),"\109\111\117\115\101")end;h["\71\53\95\80\82\69\83\83\69\68"]=function()h["\71\53\95\95\95"]=e;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\80\82\69\83\83\69\68",j("\53"),"\109\111\117\115\101")end;h["\71\53\95\82\69\76\69\65\83\69\68"]=function()h["\71\53\95\95\95"]=d;h["\79\110\69\118\101\110\116"]("\77\79\85\83\69\95\66\85\84\84\79\78\95\82\69\76\69\65\83\69\68",j("\53"),"\109\111\117\115\101")end;while e do while h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\49"))and not h["\71\49\95\95\95"]do h["\71\49\95\80\82\69\83\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;while not h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\49"))and h["\71\49\95\95\95"]do h["\71\49\95\82\69\76\69\65\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;while h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\51"))and not h["\71\50\95\95\95"]do h["\71\50\95\80\82\69\83\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;while not h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\51"))and h["\71\50\95\95\95"]do h["\71\50\95\82\69\76\69\65\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;while h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\50"))and not h["\71\51\95\95\95"]do h["\71\51\95\80\82\69\83\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;while not h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\50"))and h["\71\51\95\95\95"]do h["\71\51\95\82\69\76\69\65\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;while h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\52"))and not h["\71\52\95\95\95"]do h["\71\52\95\80\82\69\83\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;while not h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\52"))and h["\71\52\95\95\95"]do h["\71\52\95\82\69\76\69\65\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;while h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\53"))and not h["\71\53\95\95\95"]do h["\71\53\95\80\82\69\83\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;while not h["\73\115\77\111\117\115\101\66\117\116\116\111\110\80\114\101\115\115\101\100"](j("\53"))and h["\71\53\95\95\95"]do h["\71\53\95\82\69\76\69\65\83\69\68"]()break;h["\83\108\101\101\112"](j("\49"))end;h["\83\108\101\101\112"](j("\49"))end end;k=k(...)return k

