# 罗技Lua脚本键盘监听功能说明

## 功能概述

已成功在现有的"罗技lua代码（单击开镜）不带burst模式数据.lua"文件中添加了键盘监听功能，实现了基于键盘输入的武器配置文件路径切换。

## 监听键位

脚本监听以下6个键位：
- **g** - 切换到加强配置
- **x** - 切换到加强配置  
- **1** - 切换到默认配置
- **2** - 切换到默认配置
- **3** - 切换到加强配置
- **4** - 切换到加强配置

## 路径切换逻辑

### 切换到加强配置 (jia.lua)
按下以下任意键位时，脚本会切换到 `C:/Temp/jia.lua`：
- **g键**
- **x键** 
- **3键**
- **4键**

### 切换到默认配置 (weapon.lua)
按下以下任意键位时，脚本会切换到 `C:/Temp/weapon.lua`：
- **1键**
- **2键**

## 主要修改内容

### 1. 新增变量
```lua
-- 键盘监听和路径切换相关变量
local addr = "C:/Temp/weapon.lua"  -- 默认路径
local weapon_path = "C:/Temp/weapon.lua"  -- weapon路径
local jia_path = "C:/Temp/jia.lua"        -- jia路径
local current_path = weapon_path           -- 当前使用的路径

-- 键盘监听状态变量
local keyboard_monitoring_enabled = true  -- 键盘监听开关
```

### 2. 新增函数

#### 路径切换函数
- `switch_to_jia_path()` - 切换到加强配置路径
- `switch_to_weapon_path()` - 切换到默认配置路径
- `handle_keyboard_input(key)` - 处理键盘输入的主函数

#### 初始化函数
- `initialize_keyboard_monitoring()` - 键盘监听功能初始化

### 3. 修改的OnEvent函数

在原有的`OnEvent`函数中添加了`G_PRESSED`事件处理：
- 监听键盘按键事件
- 根据按键类型调用相应的路径切换函数
- 路径切换后自动重新加载武器配置

### 4. 修改的主循环

在原有的无限循环中添加了键盘监听逻辑：
- 使用`IsKeyPressed()`函数检测键盘状态
- 防止重复触发的状态管理
- 调用`OnEvent`函数处理键盘事件

## 调试输出

脚本会输出详细的调试信息：

### 初始化信息
```
=== 罗技键盘监听功能初始化 ===
监听键位: g, x, 1, 2, 3, 4
路径切换规则:
  g, x, 3, 4 -> C:/Temp/jia.lua
  1, 2 -> C:/Temp/weapon.lua
当前路径: C:/Temp/weapon.lua
键盘监听状态: 启用
================================
```

### 运行时信息
- 配置文件激活/禁用状态
- 按键检测信息
- 路径切换确认
- 武器配置重新加载确认

## 使用方法

1. **准备配置文件**
   - 确保 `C:/Temp/weapon.lua` 存在（默认武器配置）
   - 确保 `C:/Temp/jia.lua` 存在（加强武器配置）

2. **启动脚本**
   - 在罗技G-Hub中加载修改后的Lua脚本
   - 脚本启动时会自动初始化键盘监听功能

3. **使用键盘切换**
   - 游戏中按下 g/x/3/4 键切换到加强配置
   - 按下 1/2 键切换回默认配置
   - 每次切换都会自动重新加载对应的武器配置

## 兼容性

- 保持了原有脚本的所有功能
- 不影响现有的鼠标事件处理
- 不影响现有的压枪和调试功能
- 键盘监听可以通过 `keyboard_monitoring_enabled` 变量控制开关

## 注意事项

1. **文件路径**：确保配置文件路径正确且文件存在
2. **权限**：确保脚本有读取配置文件的权限
3. **键位冲突**：避免与游戏内键位设置冲突
4. **性能**：键盘监听在主循环中运行，对性能影响极小

## 故障排除

如果键盘监听不工作：
1. 检查 `keyboard_monitoring_enabled` 是否为 `true`
2. 检查配置文件路径是否正确
3. 查看罗技G-Hub的日志输出
4. 确认罗技设备驱动正常工作

## 扩展可能

如需添加更多监听键位或修改路径切换逻辑，可以：
1. 在主循环中添加更多 `IsKeyPressed()` 检查
2. 在 `handle_keyboard_input()` 函数中添加新的键位处理逻辑
3. 添加更多配置文件路径选项
