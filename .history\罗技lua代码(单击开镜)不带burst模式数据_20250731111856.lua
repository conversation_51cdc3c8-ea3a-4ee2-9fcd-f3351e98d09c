EnablePrimaryMouseButtonEvents(true)
--ctrl+鼠标4是调试模式
--ctrl+鼠标中键是一键捡枪
--所有连狙都默认是连点，比如MINI的1-30发子弹的下压值全部都是20,连狙采用双弹道default用单点弹道且每一发下压值都一样，burst是长按左ctrl时的弹道，则采用和步枪类似的弹道
--所有鼠标均可用
--调弹道就去看3弹道调试视频

--悬浮窗说明：
--800x40：窗口宽度为 800 像素，高度为 40 像素。
--+500+1：窗口左上角距离屏幕左边的距离为 500 像素，距离屏幕顶部的距离为 1 像素。

--键盘监听功能说明：
--监听键位：g, x, 1, 2, 3, 4
--路径切换逻辑：
--  按下 g, x, 3, 4 中任意一个键：切换到 C:/Temp/jia.lua
--  按下 1, 2 中任意一个键：切换到 C:/Temp/weapon.lua

--配件注释 poses = {
            --None = 1,无姿势
            --stand = 1,站姿
            --down = 1,蹲姿

        --muzzles = {
            --None = 1,无枪口
            --xy1 = 1,步枪消炎
            --xy2 = 1,狙消炎
            --xy3 = 1,冲锋枪消炎
            --bc1 = 1,步枪补偿
            --bc2 = 1,狙补偿
            --bc3 = 1,冲锋枪补偿
            --xx = 1,消音
            --xx1 = 1,冲锋枪消音
            --zt = 1,制退器

        --grips = {
            --None = 1,无握把
            --angle = 1, 三角握把
            --light = 1，轻型
            --red = 1，红握
            --line = 1,垂直
            --thumb = 1,拇指

        --scopes = {
            --None = 1,无倍镜
            --reddot = 1,红点
            --quanxi = 1,全息
            --x2 = 1,
            --x3 = 1,
            --x4 = 1,
            --x6 = 1,
            --x8 = 1,

        --stocks = {
            --None = 1,无枪托
            --normal = 1,枪托
            --heavy = 1,重型枪托
            --pg = 1,托腮板

-- 全局压枪系数，初始值为100%，可以通过修改此值调整
local global_recoil_multiplier = 100 / 100

-- 键盘监听和路径切换相关变量
local addr = "C:/Temp/weapon.lua"  -- 默认路径
local weapon_path = "C:/Temp/weapon.lua"  -- weapon路径
local jia_path = "C:/Temp/jia.lua"        -- jia路径
local current_path = weapon_path           -- 当前使用的路径

-- 键盘监听状态变量
local keyboard_monitoring_enabled = true  -- 键盘监听开关
-- 路径切换函数
function switch_to_jia_path()
    if current_path ~= jia_path then
        current_path = jia_path
        addr = current_path
        OutputLogMessage("键盘监听: 路径已切换到 %s\n", current_path)
        return true
    end
    return false
end

function switch_to_weapon_path()
    if current_path ~= weapon_path then
        current_path = weapon_path
        addr = current_path
        OutputLogMessage("键盘监听: 路径已切换到 %s\n", current_path)
        return true
    end
    return false
end

-- 键盘监听处理函数
function handle_keyboard_input(key)
    if not keyboard_monitoring_enabled then
        return false
    end

    -- 检查是否是我们监听的键位
    if key == "g" or key == "x" or key == "3" or key == "4" then
        -- 切换到 jia.lua
        return switch_to_jia_path()
    elseif key == "1" or key == "2" then
        -- 切换到 weapon.lua
        return switch_to_weapon_path()
    end

    return false
end

function read_weapon_from_file()
    weapon_name = nil
    scopes = nil
    muzzles = nil
    stocks= nil
    poses = nil
    shoot = nil
    car = nil

    dofile(addr)

    if weapon_name then

        last_weapon_name = weapon_name
        last_muzzles = muzzles
        last_grips = grips
        last_scopes = scopes
        last_stocks = stocks
        last_poses = poses
        last_shoot = shoot
        last_car = car

        local output = string.format("%s+%s+%s+%s+%s+%s+%s+%s+%s",weapon_name, muzzles, grips, scopes, stocks, poses , scope_zoom, shoot, car)
        OutputLogMessage("%s\n", output)
        return weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom, shoot, car
    else
        OutputLogMessage("未找到武器信息, 使用上一次的武器信息\n")

        return last_weapon_name, last_muzzles, last_grips, last_scopes, last_stocks, last_poses, last_scope_zoom, last_shoot, last_car
    end
end
            
function read_poses_file()
    poses = nil

    dofile(addr)

    if weapon_name then

        last_poses = poses

        return poses
    else
        return last_poses
    end
end

local attachment_multipliers = {
None = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.78,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },
    Berry = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.83,
            crawl = 0.58,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.8,
            xx = 1,
            zt = 0.86,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.8,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.444,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   AUG = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.8,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.85,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.8,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.444,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   AKM = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.47,
        },
        muzzles = {
            None = 1,
            xy1 = 0.85,
            bc1 = 0.78,
            xx = 1,
            zt = 0.85,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.444,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   M416 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.78,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   ACE32 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.78,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   G36C = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.78,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   SCAR = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.78,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   QBZ = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.78,
            thumb = 0.83,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   K2 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   M16 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.84,
            bc1 = 0.75,
            xx = 1,
            zt = 0.84,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1.8,
            x3 = 2.6,
            x4 = 3.6,
            x6 = 2.6,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   MK47 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,
            crawl = 0.53,
        },
        muzzles = {
            None = 1,
            xy1 = 0.84,
            bc1 = 0.75,
            xx = 1,
            zt = 0.84,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.78,
            thumb = 0.83,
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1.8,
            x3 = 2.6,
            x4 = 3.6,
            x6 = 2.6,
        },
        stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   GROZA = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.7,
            crawl = 0.47,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.78,
            xx = 1,
            zt = 0.87,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   FAMAS = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.74,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            xy1 = 0.86,
            bc1 = 0.8,
            xx = 1,
            zt = 0.83,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.555,
            reddot = 0.555,
            quanxi = 0.555,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   PP19 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.77,
            crawl = 0.7,
        },
        muzzles = {
            None = 1,
            bc3 = 0.8,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
            line = 0.78,
        },
        scopes = {
            None = 0.51,
            reddot = 0.51,
            quanxi = 0.51,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   TOM = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.7,
            crawl = 0.62,
        },
        muzzles = {
            None = 1,
            xx1 = 1,
        },
        grips = {
            None = 1,
            line = 0.78	,
        },
        scopes = {
            None = 0.51,
            reddot = 0.51,
            quanxi = 0.51,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   UMP = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.74,
            crawl = 0.7,
        },
        muzzles = {
            None = 1,
            bc3 = 0.8,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   UZI = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.7,
            crawl = 0.6,
        },
        muzzles = {
            None = 1,
            bc3 = 0.7,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.51,
            reddot = 0.51,
            quanxi = 0.51,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   VECTOR = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,
            crawl = 0.64,
        },
        muzzles = {
            None = 1,
            bc3 = 0.8,
            xy3 = 0.9,
            xx1 = 1,
        },
        grips = {
            None = 1,
            red = 0.83,
            line = 0.79,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
            normal = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   MP5 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.67,
            crawl = 0.53,
        },
        muzzles = {
            None = 1,
            bc3 = 0.6,
            xy3 = 0.82,
            xx1 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.75,
            thumb = 0.83,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
            normal = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   P90 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.77,
            crawl = 0.67,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   JS9 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.73,
            crawl = 0.5,
        },
        muzzles = {
            None = 1,
            bc3 = 0.58,
            xy3 = 0.82,
            xx1 = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
            x2 = 1,
            x3 = 1.444,
            x4 = 2,
            x6 = 1.455,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },

   MP9 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.73,
            crawl = 0.63,
        },
        muzzles = {
            None = 1,
        },
        grips = {
            None = 1,
        },
        scopes = {
            None = 0.52,
            reddot = 0.52,
            quanxi = 0.52,
        },
        stocks = {
            None = 1,
        },
        car = {
            None = 1,
            car = 1.5,
        },
    },
 SLR = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,  
        },
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 2.5,
            reddot = 2.5,
            quanxi = 2.5,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 0.9,
            x8 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    MINI = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.8,  
        },
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 1.5,
            reddot =1.5,
            quanxi = 1.5,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 1,
            x8 = 1.1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    SKS = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.75,  
        },
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 2.5,
            reddot = 2.5,
            quanxi = 2.5,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 1,
            x8 = 1.1,
        },
         grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    MK12 = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,  
        },
        
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 1.5,
            reddot = 1.5,
            quanxi = 1.5,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 1,
            x8 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    QBU = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.78,  
        },
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 1.5,
            reddot = 1.5,
            quanxi = 1.5,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 1,
            x8 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    DLG = { 
        poses = {
            None = 1,
            stand = 1,
            down = 0.8,  
        },
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 2.5,
            reddot = 2.5,
            quanxi = 2.5,
            x2 = 1,
            x3 = 1,
            x4 = 1.3,
            x6 = 1,
            x8 = 1.4,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    VSS = { 
        poses = {
            None = 1,
            stand = 1,
            down = 1,  
        },
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 1,
                   
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1,
            x6 = 1,
            x8 = 1,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
    MK14 = { 
        poses = {
            None = 2.5,
            stand = 2.5,
            down = 0.77,  
        },
         muzzles = {
            None = 1,
            xy1 = 1,
            xy2 = 1,
            bc1 = 1,
            bc2 = 1,
            xx = 1,
            zt = 0.85,
                   
        },
        scopes = {
            None = 1,
            reddot = 1,
            quanxi = 1,
            x2 = 1,
            x3 = 1,
            x4 = 1.1,
            x6 = 1,
            x8 = 1.2,
        },
        grips = {
            None = 1,
            angle = 1, 
            red = 0.83,
            line = 0.79,
            thumb = 0.83,
        },
         stocks = {
            None = 1,
            normal = 1,
            heavy = 0.86,
        },
        car = {
            None = 1,
            car = 1.5,
        },

    },
}
    



local recoil_patterns = {
    Berry = {
        default = {
{1, 18},
{2, 15},
{3, 17},
{4, 20},
{5, 21},
{6, 21},
{7, 21},
{8, 23},
{9, 25},
{10, 25},
{11, 25},
{12, 25},
{13, 25},
{14, 25},
{15, 29},
{16, 29},
{17, 30},
{18, 30},
{19, 30},
{20, 30},
{21, 30},
{22, 30},
{23, 30},
{24, 30},
{25, 30},
{26, 30},
{27, 30},
{28, 30},
{29, 30},
{30, 30},
{31, 30},
{32, 30},
{33, 30},
{34, 30},
{35, 30},
{36, 30},
{37, 30},
{38, 30},
{39, 30},
{40, 30},
{41, 30},
        },
    },
None = {
        default = {
{1, 16},
{2, 7},
{3, 11},
{4, 12},
{5, 14},
{6, 17},
{7, 17},
{8, 17},
{9, 18},
{10, 19},
{11, 19},
{12, 19},
{13, 19},
{14, 19},
{15, 21},
{16, 21},
{17, 21},
{18, 21},
{19, 21},
{20, 21},
{21, 21},
{22, 21},
{23, 21},
{24, 22},
{25, 22},
{26, 22},
{27, 22},
{28, 22},
{29, 22},
{30, 23},
{31, 23},
{32, 23},
{33, 23},
{34, 23},
{35, 23},
{36, 23},
{37, 23},
{38, 23},
{39, 23},
{40, 23},
{41, 23},
        },
    },
   

    AUG = {
        default = {
{1, 18},
{2, 9},
{3, 9},
{4, 13},
{5, 16},
{6, 18},
{7, 20},
{8, 20},
{9, 20},
{10, 21},
{11, 23},
{12, 25},
{13, 25},
{14, 25},
{15, 26},
{16, 26},
{17, 27},
{18, 27},
{19, 27},
{20, 27},
{21, 27},
{22, 27},
{23, 27},
{24, 27},
{25, 27},
{26, 27},
{27, 27},
{28, 27},
{29, 27},
{30, 27},
{31, 27},
{32, 28},
{33, 28},
{34, 28},
{35, 28},
{36, 28},
{37, 28},
{38, 28},
{39, 28},
{40, 28},
{41, 28},
        },
    },

    AKM = {
        default = {
{1, 18},
{2, 9},
{3, 9},
{4, 12},
{5, 16},
{6, 16},
{7, 16},
{8, 18},
{9, 20},
{10, 20},
{11, 21},
{12, 21},
{13, 21},
{14, 21},
{15, 21},
{16, 22},
{17, 22},
{18, 22},
{19, 22},
{20, 22},
{21, 22},
{22, 22},
{23, 23},
{24, 23},
{25, 23},
{26, 23},
{27, 23},
{28, 23},
{29, 23},
{30, 23},
{31, 23},
{32, 23},
{33, 23},
{34, 23},
{35, 23},
{36, 23},
{37, 23},
{38, 23},
{39, 23},
{40, 23},
{41, 23},
        },
    },  

    M416 = {
        default = {
{1, 16},
{2, 7},
{3, 11},
{4, 12},
{5, 14},
{6, 17},
{7, 17},
{8, 17},
{9, 18},
{10, 19},
{11, 19},
{12, 19},
{13, 19},
{14, 19},
{15, 21},
{16, 21},
{17, 21},
{18, 21},
{19, 21},
{20, 21},
{21, 21},
{22, 21},
{23, 21},
{24, 22},
{25, 22},
{26, 22},
{27, 22},
{28, 22},
{29, 22},
{30, 23},
{31, 23},
{32, 23},
{33, 23},
{34, 23},
{35, 23},
{36, 23},
{37, 23},
{38, 23},
{39, 23},
{40, 23},
{41, 23},
        },
    },

    ACE32 = {
        default = {
{1, 18},
{2, 12},
{3, 12},
{4, 15},
{5, 17},
{6, 19},
{7, 20},
{8, 20},
{9, 21},
{10, 21},
{11, 21},
{12, 22},
{13, 23},
{14, 23},
{15, 23},
{16, 23},
{17, 23},
{18, 24},
{19, 25},
{20, 26},
{21, 26},
{22, 26},
{23, 26},
{24, 26},
{25, 27},
{26, 27},
{27, 27},
{28, 27},
{29, 27},
{30, 27},
{31, 27},
{32, 27},
{33, 27},
{34, 27},
{35, 27},
{36, 27},
{37, 27},
{38, 27},
{39, 27},
{40, 27},
{41, 27},
        },
    },

    G36C = {
        default = {
{1, 16},
{2, 7},
{3, 11},
{4, 12},
{5, 14},
{6, 17},
{7, 17},
{8, 17},
{9, 18},
{10, 19},
{11, 19},
{12, 19},
{13, 19},
{14, 19},
{15, 21},
{16, 21},
{17, 21},
{18, 21},
{19, 21},
{20, 21},
{21, 21},
{22, 21},
{23, 21},
{24, 22},
{25, 22},
{26, 22},
{27, 22},
{28, 22},
{29, 22},
{30, 23},
{31, 23},
{32, 23},
{33, 23},
{34, 23},
{35, 23},
{36, 23},
{37, 23},
{38, 23},
{39, 23},
{40, 23},
{41, 23},
        },
    },

    SCAR = {
        default = {
{1, 16},
{2, 7},
{3, 11},
{4, 12},
{5, 14},
{6, 17},
{7, 17},
{8, 17},
{9, 18},
{10, 19},
{11, 19},
{12, 19},
{13, 19},
{14, 19},
{15, 21},
{16, 21},
{17, 21},
{18, 21},
{19, 21},
{20, 21},
{21, 21},
{22, 21},
{23, 21},
{24, 22},
{25, 22},
{26, 22},
{27, 22},
{28, 22},
{29, 22},
{30, 23},
{31, 23},
{32, 23},
{33, 23},
{34, 23},
{35, 23},
{36, 23},
{37, 23},
{38, 23},
{39, 23},
{40, 23},
{41, 23},
        },
    },

    QBZ = {
        default = {
{1, 16},
{2, 7},
{3, 11},
{4, 12},
{5, 14},
{6, 17},
{7, 17},
{8, 17},
{9, 18},
{10, 19},
{11, 19},
{12, 19},
{13, 19},
{14, 19},
{15, 21},
{16, 21},
{17, 21},
{18, 21},
{19, 21},
{20, 21},
{21, 21},
{22, 21},
{23, 21},
{24, 22},
{25, 22},
{26, 22},
{27, 22},
{28, 22},
{29, 22},
{30, 23},
{31, 23},
{32, 23},
{33, 23},
{34, 23},
{35, 23},
{36, 23},
{37, 23},
{38, 23},
{39, 23},
{40, 23},
{41, 23},
        },
    },

    K2 = {
        default = {
{1, 16},
{2, 7},
{3, 11},
{4, 12},
{5, 14},
{6, 17},
{7, 17},
{8, 17},
{9, 18},
{10, 19},
{11, 19},
{12, 19},
{13, 19},
{14, 19},
{15, 21},
{16, 21},
{17, 21},
{18, 21},
{19, 21},
{20, 21},
{21, 21},
{22, 21},
{23, 21},
{24, 22},
{25, 22},
{26, 22},
{27, 22},
{28, 22},
{29, 22},
{30, 23},
{31, 23},
{32, 23},
{33, 23},
{34, 23},
{35, 23},
{36, 23},
{37, 23},
{38, 23},
{39, 23},
{40, 23},
{41, 23},
        },
    },

    M16 = {
        default = {
{1, 6},
{2, 6},
{3, 7},
{4, 7},
{5, 8},
{6, 12},
{7, 12},
{8, 12},
{9, 12},
{10, 12},
{11, 12},
{12, 12},
{13, 12},
{14, 12},
{15, 12},
{16, 12},
{17, 12},
{18, 12},
{19, 12},
{20, 12},
{21, 12},
{22, 12},
{23, 12},
{24, 12},
{25, 12},
{26, 12},
{27, 12},
{28, 12},
{29, 12},
{30, 11},
{31, 11},
{32, 11},
{33, 11},
{34, 11},
{35, 12},
{36, 12},
{37, 12},
{38, 12},
{39, 12},
       },
    },

    MK47 = {
        default = {
{1, 6},
{2, 6},
{3, 9},
{4, 9},
{5, 10},
{6, 14},
{7, 14},
{8, 14},
{9, 14},
{10, 14},
{11, 14},
{12, 14},
{13, 14},
{14, 14},
{15, 14},
{16, 14},
{17, 14},
{18, 14},
{19, 14},
{20, 14},
{21, 14},
{22, 14},
{23, 14},
{24, 14},
{25, 14},
{26, 14},
{27, 14},
{28, 14},
{29, 14},
{30, 14},
       },
    },

    GROZA = {
        default = {
{1, 16},
{2, 8},
{3, 13},
{4, 14},
{5, 14},
{6, 14},
{7, 14},
{8, 14},
{9, 14},
{10, 15},
{11, 15},
{12, 16},
{13, 16},
{14, 17},
{15, 18},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 21},
{21, 21},
{22, 21},
{23, 21},
{24, 21},
{25, 21},
{26, 21},
{27, 21},
{28, 21},
{29, 21},
{30, 21.5},
{31, 21.5},
{32, 21.5},
{33, 21.5},
{34, 21.5},
{35, 21.5},
{36, 21.5},
{37, 21.5},
{38, 21.5},
{39, 21.5},
{40, 21.5},
{41, 21.5},
        },
    },

    FAMAS = {
        default = {
{1, 18},
{2, 6},
{3, 9},
{4, 9},
{5, 10},
{6, 14},
{7, 17},
{8, 18},
{9, 19},
{10, 20},
{11, 21},
{12, 21},
{13, 21},
{14, 21},
{15, 22},
{16, 22},
{17, 22},
{18, 22},
{19, 22},
{20, 22},
{21, 23},
{22, 23},
{23, 23},
{24, 23},
{25, 23},
{26, 23},
{27, 23},
{28, 23},
{29, 23},
{30, 23},
      },
    },

    PP19 = {
        default = {
{1, 7},
{2, 7},
{3, 8},
{4, 9},
{5, 10},
{6, 11},
{7, 12},
{8, 12},
{9, 12},
{10, 12},
{11, 12},
{12, 10.5},
{13, 10.5},
{14, 10.5},
{15, 10.5},
{16, 10.5},
{17, 10.5},
{18, 10.5},
{19, 10.5},
{20, 10.5},
{21, 10.5},
{22, 10.5},
{23, 10.5},
{24, 10.5},
{25, 10.5},
{26, 10.5},
{27, 10.5},
{28, 10.5},
{29, 10.5},
{30, 10.5},
{31, 10.5},
{32, 10.5},
{33, 10.5},
{34, 10.5},
{35, 10.5},
{36, 10.5},
{37, 10.5},
{38, 10.5},
{39, 10.5},
{40, 10.5},
{41, 10.5},
{42, 10.5},
{43, 10.5},
{44, 10.5},
{45, 10.5},
{46, 10.5},
{47, 10.5},
{48, 10.5},
{49, 10.5},
{50, 10.5},
{51, 10.5},
{52, 10.5},
{53, 10.5},
      },
    },

    TOM = {
        default = {
{1, 8},
{2, 8},
{3, 9},
{4, 11},
{5, 12},
{6, 12},
{7, 13},
{8, 13},
{9, 14},
{10, 15},
{11, 16},
{12, 22},
{13, 22},
{14, 22},
{15, 22},
{16, 22},
{17, 22},
{18, 22},
{19, 22},
{20, 22},
{21, 22},
{22, 22},
{23, 22},
{24, 22},
{25, 22},
{26, 22},
{27, 20},
{28, 20},
{29, 20},
{30, 20},
{31, 20},
{32, 20},
{33, 20},
{34, 20},
{35, 20},
{36, 20},
{37, 20},
{38, 20},
{39, 20},
{40, 20},
{41, 20},
{42, 20},
{43, 20},
{44, 20},
{45, 20},
{46, 20},
{47, 20},
{48, 20},
{49, 20},
{50, 20},
      },
    },

    UMP = {
        default = {
{1, 9},
{2, 9},
{3, 10},
{4, 11},
{5, 12},
{6, 13},
{7, 13},
{8, 13},
{9, 14},
{10, 14},
{11, 14},
{12, 14.5},
{13, 14.5},
{14, 14.5},
{15, 14.5},
{16, 14.5},
{17, 16},
{18, 16},
{19, 16},
{20, 16},
{21, 16},
{22, 16},
{23, 16},
{24, 16},
{25, 16},
{26, 16},
{27, 16},
{28, 16},
{29, 16},
{30, 16},
{31, 16},
{32, 16},
{33, 16},
{34, 16},
{35, 16},
      },
    },

    UZI = {
        default = {
{1, 7},
{2, 7},
{3, 8},
{4, 8},
{5, 9},
{6, 10},
{7, 11},
{8, 12},
{9, 13},
{10, 14},
{11, 14},
{12, 20.5},
{13, 20.5},
{14, 20.5},
{15, 20.5},
{16, 20.5},
{17, 20.5},
{18, 20.5},
{19, 20.5},
{20, 22.5},
{21, 22.5},
{22, 22.5},
{23, 22.5},
{24, 22.5},
{25, 22.5},
{26, 22.5},
{27, 22.5},
{28, 23.5},
{29, 23.5},
{30, 23.5},
{31, 23.5},
{32, 23.5},
{33, 23.5},
{34, 23.5},
{35, 23.5},
      },
    },

    VECTOR = {
        default = {
{1, 11},
{2, 10},
{3, 11},
{4, 12},
{5, 13},
{6, 14},
{7, 15},
{8, 16},
{9, 17},
{10, 17},
{11, 19},
{12, 20.5},
{13, 22.5},
{14, 24.5},
{15, 27.5},
{16, 27.5},
{17, 27.5},
{18, 27.5},
{19, 27.5},
{20, 27.5},
{21, 27.5},
{22, 27.5},
{23, 27.5},
{24, 27.5},
{25, 27.5},
{26, 27.5},
{27, 27.5},
{28, 27.5},
{29, 27.5},
{30, 27.5},
{31, 27.5},
{32, 27.5},
{33, 27.5},
      },
    },

    MP5 = {
        default = {
{1, 16},
{2, 9},
{3, 9},
{4, 12},
{5, 14},
{6, 15},
{7, 16},
{8, 17},
{9, 18},
{10, 19},
{11, 19},
{12, 19},
{13, 19},
{14, 19.5},
{15, 19.5},
{16, 19.5},
{17, 19.5},
{18, 19.5},
{19, 19.5},
{20, 19.5},
{21, 19.5},
{22, 19.5},
{23, 19.5},
{24, 19.5},
{25, 19.5},
{26, 19.5},
{27, 19.5},
{28, 19.5},
{29, 19.5},
{30, 19.5},
{31, 20},
{32, 20},
{33, 20},
{34, 20},
{35, 20},
{36, 20},
{37, 20},
{38, 20},
{39, 20},
{40, 20},
      },
    },

    P90 = {
        default = {
{1, 8},
{2, 8},
{3, 9},
{4, 11},
{5, 12},
{6, 12},
{7, 13},
{8, 13},
{9, 13},
{10, 12},
{11, 14},
{12, 14},
{13, 15},
{14, 15},
{15, 12},
{16, 9.1},
{17, 9.1},
{18, 9.1},
{19, 9.1},
{20, 9.1},
{21, 9.1},
{22, 9.1},
{23, 9.1},
{24, 9.1},
{25, 9.1},
{26, 9.1},
{27, 9.1},
{28, 9.1},
{29, 9.1},
{30, 9.1},
{31, 9.1},
{32, 9.1},
{33, 9.1},
{34, 9.1},
{35, 9.1},
{36, 9.1},
{37, 9.1},
{38, 9.1},
{39, 9.1},
{40, 9.1},
{41, 9.1},
{42, 9.1},
{43, 9.1},
{44, 9.1},
{45, 9.1},
{46, 9.1},
{47, 9.1},
{48, 9.1},
{49, 9.1},
{50, 9.1},
      },
    },

    JS9 = {
        default = {
{1, 10},
{2, 7},
{3, 7},
{4, 10},
{5, 12},
{6, 13},
{7, 13},
{8, 13},
{9, 13},
{10, 13},
{11, 13},
{12, 13},
{13, 15},
{14, 18.5},
{15, 18.5},
{16, 18.5},
{17, 18.5},
{18, 18.5},
{19, 18.5},
{20, 18.5},
{21, 18.5},
{22, 18.5},
{23, 18.5},
{24, 18.5},
{25, 18.5},
{26, 18.5},
{27, 18.5},
{28, 18.5},
{29, 18.5},
{30, 18.5},
{31, 18.5},
{32, 18.5},
{33, 18.5},
{34, 18.5},
{35, 18.5},
{36, 18.5},
{37, 18.5},
{38, 18.5},
{39, 18.5},
{40, 18.5},
      },
    },

    MP9 = {
        default = {
{1, 7},
{2, 7},
{3, 8},
{4, 8},
{5, 9},
{6, 10},
{7, 11},
{8, 12},
{9, 12},
{10, 12},
{11, 12},
{12, 12.5},
{13, 12.5},
{14, 12.5},
{15, 12.5},
{16, 12.5},
{17, 12.5},
{18, 11.5},
{19, 8.5},
{20, 8.5},
{21, 7.5},
{22, 7.5},
{23, 7.5},
{24, 7.5},
{25, 7.5},
{26, 7.5},
{27, 7.5},
{28, 7.5},
{29, 7.5},
{30, 7.5},
{31, 7.5},
{32, 7.5},
{33, 7.5},
{34, 7.5},
{35, 7.5},
      },
    },
    
    
    SLR = {
        default = {
{1, 0},
{2, 0},
{3, 0},
{4, 0},
{5, 0},
{6, 0},
{7, 0},
{8, 0},
{9, 0},
{10, 0},
{11, 0},
{12, 0},
{13, 0},
{14, 0},
{15, 0},
{16, 0},
{17, 0},
{18, 0},
{19, 0},
{20, 0},
        },
    },
    
    MINI = {
        default = {
{1, 5},
{2, 5},
{3, 5},
{4, 5},
{5, 5},
{6, 5},
{7, 5},
{8, 5},
{9, 5},
{10, 5},
{11, 0},
{12, 0},
{13, 0},
{14, 0},
{15, 0},
{16, 0},
{17, 0},
{18, 0},
{19, 0},
{20, 0},
        },
        burst = {
-- 这里填写burst模式弹道数据：
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
SLR = {
        default = {
{1, 5},
{2, 5},
{3, 5},
{4, 5},
{5, 5},
{6, 5},
{7, 5},
{8, 5},
{9, 5},
{10, 5},
{11, 0},
{12, 0},
{13, 0},
{14, 0},
{15, 0},
{16, 0},
{17, 0},
{18, 0},
{19, 0},
{20, 0},
        },
        burst = {
-- 这里填写burst模式弹道数据：
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    }, 
    SKS = {
        default = {
{1, 5},
{2, 5},
{3, 5},
{4, 5},
{5, 5},
{6, 5},
{7, 5},
{8, 5},
{9, 5},
{10, 5},
{11, 0},
{12, 0},
{13, 0},
{14, 0},
{15, 0},
{16, 0},
{17, 0},
{18, 0},
{19, 0},
{20, 0},
        },
        DLG = {
-- 这里填写burst模式弹道数据：
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    QBU = {
        default = {
{1, 5},
{2, 5},
{3, 5},
{4, 5},
{5, 5},
{6, 5},
{7, 5},
{8, 5},
{9, 5},
{10, 5},
{11, 0},
{12, 0},
{13, 0},
{14, 0},
{15, 0},
{16, 0},
{17, 0},
{18, 0},
{19, 0},
{20, 0},
        },
        burst = {
-- 这里填写burst模式弹道数据：
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    MK12 = {
        default = {
{1, 5},
{2, 5},
{3, 5},
{4, 5},
{5, 5},
{6, 5},
{7, 5},
{8, 5},
{9, 5},
{10, 5},
{11, 0},
{12, 0},
{13, 0},
{14, 0},
{15, 0},
{16, 0},
{17, 0},
{18, 0},
{19, 0},
{20, 0},
        },
        burst = {
-- 这里填写burst模式弹道数据：
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    MK14 = {
        default = {
{1, 5},
{2, 5},
{3, 5},
{4, 5},
{5, 5},
{6, 5},
{7, 5},
{8, 5},
{9, 5},
{10, 5},
{11, 0},
{12, 0},
{13, 0},
{14, 0},
{15, 0},
{16, 0},
{17, 0},
{18, 0},
{19, 0},
{20, 0},
        },
        burst = {
-- 这里填写burst模式弹道数据：
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },
    VSS = {
        default = {
{1, 5},
{2, 5},
{3, 5},
{4, 5},
{5, 5},
{6, 5},
{7, 5},
{8, 5},
{9, 5},
{10, 5},
{11, 0},
{12, 0},
{13, 0},
{14, 0},
{15, 0},
{16, 0},
{17, 0},
{18, 0},
{19, 0},
{20, 0},
        },
        burst = {
-- 这里填写burst模式弹道数据：
{1, 20},
{2, 20},
{3, 20},
{4, 20},
{5, 20},
{6, 20},
{7, 20},
{8, 20},
{9, 20},
{10, 20},
{11, 20},
{12, 20},
{13, 20},
{14, 20},
{15, 20},
{16, 20},
{17, 20},
{18, 20},
{19, 20},
{20, 20},
        },
    },   
    
}



local weapon_intervals = {
   None = 86,
    Berry = 86, 
    AUG = 84, 
    AKM = 102, 
    M416 = 87, 
    ACE32 = 89, 
    G36C = 87, 
    SCAR = 87, 
    QBZ = 87, 
    K2 = 87, 
    M16 = 78, 
    MK47 = 76, 
    GROZA = 80, 
    FAMAS = 66, 
    PP19 = 84, 
    TOM = 80, 
    UMP = 88, 
    UZI = 48, 
    VECTOR = 54, 
    MP5 = 66, 
    P90 = 60, 
    JS9 = 66, 
    MP9 = 60, 
    DP28 = 66, 
    M249 = 66, 
    MG3 = 66, 
    MINI = 108,
    SLR = 108,
    SKS = 108,
    MK12 = 108,
    QBU = 108,
    DLG = 108,
    VSS = 108,
}


local decimal_cache = 0


function ceil_and_cache(value)
    local integer_part = math.floor(value)
    decimal_cache = decimal_cache + value - integer_part
    if decimal_cache >= 1 then
        integer_part = integer_part + 1
        decimal_cache = decimal_cache - 1
    end
    return integer_part
end


function calculate_recoil_multiplier(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom)
    local multiplier = global_recoil_multiplier 
    local weaponData = attachment_multipliers[weapon_name] 

    if weaponData then
        multiplier = multiplier * (weaponData.poses[poses] or 1)  
        multiplier = multiplier * (weaponData.muzzles[muzzles] or 1)
        multiplier = multiplier * (weaponData.grips[grips] or 1)
        multiplier = multiplier * (weaponData.scopes[scopes] or 1)
        multiplier = multiplier * (weaponData.stocks[stocks] or 1)
        multiplier = multiplier * (weaponData.car[car] or 1)
        multiplier = multiplier * scope_zoom
    end

    return multiplier
end


local burstModeEnabled = false
local debugModeEnabled = false 


local weaponBurstModes = {}


local ClickStartTime = 0


function apply_recoil(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom, car)
    local pattern_type = IsModifierPressed("rshift") and "burst" or "default"  -- 检查是否长按shift
    local pattern = recoil_patterns[weapon_name] and recoil_patterns[weapon_name][pattern_type] or recoil_patterns[weapon_name]
    local interval = weapon_intervals[weapon_name]

    if not pattern or not interval then
        OutputLogMessage("未找到武器的压枪参数: %s\n", weapon_name)
        return
    end

    local multiplier = calculate_recoil_multiplier(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom, car)
    local bullet_count = 0

    if weapon_name == "MK47" or weapon_name == "M16"  or weapon_name == "None" or weapon_name == "MINI" or weapon_name == "SKS" or weapon_name == "MK12" or weapon_name == "SLR" or weapon_name == "QBU" then 
        
        while IsMouseButtonPressed(1) do  
         if shoot == "None" then 
                break
            end
            local ClickCurrentTime = GetRunningTime()
            bullet_count = math.ceil((ClickCurrentTime - ClickStartTime) / interval)  

            
            for _, recoil_data in ipairs(pattern) do
                if recoil_data[1] == bullet_count then
                   poses = read_poses_file()
                    multiplier = calculate_recoil_multiplier(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom, car)
                    local adjusted_recoil = ceil_and_cache(recoil_data[2] * multiplier)
                    MoveMouseRelative(0, adjusted_recoil)
                    PressAndReleaseKey("F8")
                    if not IsMouseButtonPressed(1) then
                        break
                    end
                    if debugModeEnabled then
                        MoveMouseRelative(2, 0) 
                    end
                    break  
                end
            end

            Sleep(1)  
        end
    elseif weaponBurstModes[weapon_name] then 
        
        while IsMouseButtonPressed(1) do  
            local ClickCurrentTime = GetRunningTime()
            bullet_count = math.ceil((ClickCurrentTime - ClickStartTime) / interval)  

            
            for _, recoil_data in ipairs(pattern) do
            if shoot == "None" then 
                break
            end
                if recoil_data[1] == bullet_count then
                   poses = read_poses_file()
                    multiplier = calculate_recoil_multiplier(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom, car)
                    local adjusted_recoil = ceil_and_cache(recoil_data[2] * multiplier)
                    MoveMouseRelative(0, adjusted_recoil)    
                    PressAndReleaseKey("F8")
                    if not IsMouseButtonPressed(1) then
                        break
                    end
                    
                    if debugModeEnabled then
                        MoveMouseRelative(2, 0) 
                    end
                    break  
                end
            end
            Sleep(1)  
        end
    else
        
        while IsMouseButtonPressed(1) do  
             if shoot == "None" then 
                break
            end

            local ClickCurrentTime = GetRunningTime()
            bullet_count = math.ceil((ClickCurrentTime - ClickStartTime) / interval)  

            
            for _, recoil_data in ipairs(pattern) do
                if recoil_data[1] == bullet_count then
                   poses = read_poses_file()
                    multiplier = calculate_recoil_multiplier(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom, car)
                    local adjusted_recoil = ceil_and_cache(recoil_data[2] * multiplier)
                    MoveMouseRelative(0, adjusted_recoil)
                    if not IsMouseButtonPressed(1) then
                        break
                   end
                    if debugModeEnabled then
                        MoveMouseRelative(2, 0) 
                    end
                    break  
                end
            end

            Sleep(1)  
        end
    end
end


local last_weapon_name = nil
local last_muzzles = nil
local last_grips = nil
local last_scopes = nil
local last_stocks = nil
local last_poses = nil
local last_shoot = nil 




        EnablePrimaryMouseButtonEvents(true)
function OnEvent(event, arg)
    if event == "PROFILE_ACTIVATED" then
    elseif event == "PROFILE_DEACTIVATED" then
        EnablePrimaryMouseButtonEvents(false)
    elseif event == "MOUSE_BUTTON_PRESSED" then
        if arg == 1 then
            ClickStartTime = GetRunningTime()  
            PressKey("F8")
            local weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom, shoot, car = read_weapon_from_file() 
            if not scope_zoom then
    scope_zoom = 1 -- 使用默认值 1
end
            if weapon_name then
                apply_recoil(weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom, shoot, car) 
            end
        elseif arg == 2 then  
            local weapon_name, muzzles, grips, scopes, stocks, poses, scope_zoom, shoot, car = read_weapon_from_file()
            if weapon_name then
                local output = string.format("%s+%s+%s+%s+%s+%s+%s+%s",weapon_name, muzzles, grips, scopes, stocks, poses , scope_zoom, shoot, car)
                OutputLogMessage("%s\n", output)
            end
        elseif arg == 5 and IsModifierPressed("lctrl") then
            
            local weapon_name = read_weapon_from_file() 
            if weapon_name then
                weaponBurstModes[weapon_name] = not weaponBurstModes[weapon_name]
                OutputLogMessage("武器 %s 连点模式 %s\n", weapon_name, weaponBurstModes[weapon_name] and "开" or "关")
            end
        elseif arg == 4 and IsModifierPressed("lctrl") then
            debugModeEnabled = not debugModeEnabled
            OutputLogMessage("调试模式%s\n", debugModeEnabled and "开启" or "关闭")
        elseif arg == 9 then  
            fastPickup()
        end
    elseif event == "MOUSE_BUTTON_RELEASED" then
        if arg == 1 then
            ReleaseKey("F8")
            MoveMouseRelative(0, 0)
        end
    end

if (event == "MOUSE_BUTTON_PRESSED" and arg == pick and IsModifierPressed("lctrl")) then
        autopick()
    end
end

pick = 3
autopick = function()
    PressAndReleaseKey("tab")
    Sleep(1)
    for k=1,10 do 
        for j=1,5 do 
            MoveMouseTo(7800, (35000 - j * 5425)) 
            PressMouseButton(1) 
            MoveMouseTo(32767 + j * 11, 12500 + j * 12) 
            ReleaseMouseButton(1) 
            Sleep(1) 
        end 
    end   
    MoveMouseTo(32767, 32767)
    Sleep(1)
    PressAndReleaseKey("tab")
end
function G1_PRESSED() G1___ = true OnEvent("MOUSE_BUTTON_PRESSED",1,"mouse") end
function G1_RELEASED() G1___ = false OnEvent("MOUSE_BUTTON_RELEASED",1,"mouse") end
function G2_PRESSED() G2___ = true OnEvent("MOUSE_BUTTON_PRESSED",2,"mouse") end
function G2_RELEASED() G2___ = false OnEvent("MOUSE_BUTTON_RELEASED",2,"mouse") end
function G3_PRESSED() G3___ = true OnEvent("MOUSE_BUTTON_PRESSED",3,"mouse") end
function G3_RELEASED() G3___ = false OnEvent("MOUSE_BUTTON_RELEASED",3,"mouse") end
function G4_PRESSED() G4___ = true OnEvent("MOUSE_BUTTON_PRESSED",4,"mouse") end
function G4_RELEASED() G4___ = false OnEvent("MOUSE_BUTTON_RELEASED",4,"mouse") end
function G5_PRESSED() G5___ = true OnEvent("MOUSE_BUTTON_PRESSED",5,"mouse") end
function G5_RELEASED() G5___ = false OnEvent("MOUSE_BUTTON_RELEASED",5,"mouse") end
while true do
  while IsMouseButtonPressed(1) and not G1___ do G1_PRESSED() break Sleep(1) end
  while not IsMouseButtonPressed(1) and G1___ do G1_RELEASED() break Sleep(1)end
  while IsMouseButtonPressed(3) and not G2___ do G2_PRESSED() break Sleep(1) end
  while not IsMouseButtonPressed(3) and G2___ do G2_RELEASED() break Sleep(1)end
  while IsMouseButtonPressed(2) and not G3___ do G3_PRESSED() break Sleep(1) end
  while not IsMouseButtonPressed(2) and G3___ do G3_RELEASED() break Sleep(1)end
  while IsMouseButtonPressed(4) and not G4___ do G4_PRESSED() break Sleep(1) end
  while not IsMouseButtonPressed(4) and G4___ do G4_RELEASED() break Sleep(1)end
  while IsMouseButtonPressed(5) and not G5___ do G5_PRESSED() break Sleep(1) end
  while not IsMouseButtonPressed(5) and G5___ do G5_RELEASED() break Sleep(1)end
  Sleep(1)
end
