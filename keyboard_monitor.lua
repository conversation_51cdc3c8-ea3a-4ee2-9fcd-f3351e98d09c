-- Lua键盘监听和路径切换脚本
-- 使用FFI库调用Windows API实现全局键盘钩子

local ffi = require("ffi")

-- Windows API声明
ffi.cdef[[
    typedef struct {
        unsigned long vkCode;
        unsigned long scanCode;
        unsigned long flags;
        unsigned long time;
        uintptr_t dwExtraInfo;
    } KBDLLHOOKSTRUCT;

    typedef struct {
        long x;
        long y;
    } POINT;

    typedef struct {
        void* hwnd;
        unsigned int message;
        uintptr_t wParam;
        uintptr_t lParam;
        unsigned long time;
        POINT pt;
    } MSG;

    typedef uintptr_t (__stdcall *HOOKPROC)(int nCode, uintptr_t wParam, uintptr_t lParam);
    
    uintptr_t SetWindowsHookExA(int idHook, HOOKPROC lpfn, void* hMod, unsigned long dwThreadId);
    int UnhookWindowsHook(uintptr_t hhk);
    uintptr_t CallNextHookEx(uintptr_t hhk, int nCode, uintptr_t wParam, uintptr_t lParam);
    int GetMessageA(MSG* lpMsg, void* hWnd, unsigned int wMsgFilterMin, unsigned int wMsgFilterMax);
    int TranslateMessage(const MSG* lpMsg);
    uintptr_t DispatchMessageA(const MSG* lpMsg);
    void* GetModuleHandleA(const char* lpModuleName);
    void ExitProcess(unsigned int uExitCode);
    int GetAsyncKeyState(int vKey);
]]

-- 常量定义
local WH_KEYBOARD_LL = 13
local WM_KEYDOWN = 0x0100
local WM_KEYUP = 0x0101
local HC_ACTION = 0

-- 虚拟键码定义
local VK_CODES = {
    G = 0x47,
    X = 0x58,
    ['1'] = 0x31,
    ['2'] = 0x32,
    ['3'] = 0x33,
    ['4'] = 0x34,
    ESC = 0x1B  -- ESC键用于退出程序
}

-- 全局变量
local user32 = ffi.load("user32")
local kernel32 = ffi.load("kernel32")
local hook = nil
local current_path = "C:/Temp/weapon.lua"  -- 默认路径
local is_running = true

-- 路径切换函数
local function switch_path(key_pressed)
    local old_path = current_path
    
    -- 根据按键切换路径
    if key_pressed == 'g' or key_pressed == 'x' or key_pressed == '3' or key_pressed == '4' then
        current_path = "C:/Temp/jia.lua"
    elseif key_pressed == '1' or key_pressed == '2' then
        current_path = "C:/Temp/weapon.lua"
    end
    
    -- 只有路径真正改变时才输出信息
    if old_path ~= current_path then
        print(string.format("[%s] 路径切换: %s -> %s", 
            os.date("%H:%M:%S"), old_path, current_path))
    end
end

-- 获取键名
local function get_key_name(vk_code)
    for key, code in pairs(VK_CODES) do
        if code == vk_code then
            return key
        end
    end
    return nil
end

-- 键盘钩子回调函数
local function keyboard_hook_proc(nCode, wParam, lParam)
    if nCode == HC_ACTION then
        if wParam == WM_KEYDOWN then
            local kbd_struct = ffi.cast("KBDLLHOOKSTRUCT*", lParam)
            local vk_code = kbd_struct.vkCode
            local key_name = get_key_name(vk_code)
            
            if key_name then
                if key_name == "ESC" then
                    print("检测到ESC键，程序即将退出...")
                    is_running = false
                    return 0
                else
                    print(string.format("[%s] 检测到按键: %s (VK: 0x%02X)", 
                        os.date("%H:%M:%S"), key_name, vk_code))
                    switch_path(key_name)
                end
            end
        end
    end
    
    return user32.CallNextHookEx(hook, nCode, wParam, lParam)
end

-- 初始化键盘钩子
local function init_keyboard_hook()
    print("正在初始化键盘监听...")
    
    -- 获取当前模块句柄
    local hMod = kernel32.GetModuleHandleA(nil)
    if hMod == nil then
        error("无法获取模块句柄")
    end
    
    -- 创建键盘钩子回调函数
    local hook_proc = ffi.cast("HOOKPROC", keyboard_hook_proc)
    
    -- 设置全局键盘钩子
    hook = user32.SetWindowsHookExA(WH_KEYBOARD_LL, hook_proc, hMod, 0)
    if hook == 0 then
        error("无法设置键盘钩子")
    end
    
    print("键盘钩子设置成功!")
    return true
end

-- 清理资源
local function cleanup()
    if hook then
        print("正在清理键盘钩子...")
        user32.UnhookWindowsHook(hook)
        hook = nil
        print("键盘钩子已清理")
    end
end

-- 消息循环
local function message_loop()
    local msg = ffi.new("MSG")
    
    print("开始消息循环，按ESC键退出程序")
    print("当前监听的按键: g, x, 1, 2, 3, 4")
    print("当前文件路径: " .. current_path)
    print("=" .. string.rep("=", 50))
    
    while is_running do
        local result = user32.GetMessageA(msg, nil, 0, 0)
        
        if result == -1 then
            print("GetMessage错误")
            break
        elseif result == 0 then
            print("收到WM_QUIT消息")
            break
        else
            user32.TranslateMessage(msg)
            user32.DispatchMessageA(msg)
        end
    end
end

-- 主函数
local function main()
    print("Lua键盘监听程序启动")
    print("版本: 1.0")
    print("功能: 监听g,x,1,2,3,4按键并切换文件路径")
    print("")
    
    -- 设置错误处理
    local success, error_msg = pcall(function()
        -- 初始化键盘钩子
        if not init_keyboard_hook() then
            error("键盘钩子初始化失败")
        end
        
        -- 开始消息循环
        message_loop()
    end)
    
    -- 清理资源
    cleanup()
    
    if not success then
        print("程序运行出错: " .. tostring(error_msg))
        return false
    end
    
    print("程序正常退出")
    return true
end

-- 程序入口点
if not pcall(main) then
    print("程序启动失败，请检查是否有足够的权限")
    os.exit(1)
end
